# 小红书监控功能使用说明

## 功能概述

本系统集成了增强版小红书监控器，专门用于监控小红书用户的最新视频作品。该功能具有以下特点：

### 🎯 核心功能
- **智能过滤**：自动排除置顶作品和非视频内容
- **最新优先**：优先获取用户最新发布的视频作品
- **高质量下载**：使用spider_some_note进行视频下载
- **实时监控**：定时检查用户新作品，支持自动上传到目标平台

### 📊 监控逻辑
根据您提供的示例，系统会：
1. 获取用户的所有作品（包括置顶）
2. 过滤掉置顶作品（`is_pinned: True`）
3. 过滤掉非视频作品（`media_type != "video"`）
4. 按发布时间排序，获取最新的视频作品
5. 使用spider_some_note下载符合条件的作品

## 使用方法

### 1. 准备工作

#### 1.1 安装依赖
确保已安装Spider_XHS模块：
```bash
# 如果还没有安装Spider_XHS
git clone https://github.com/your-repo/Spider_XHS.git
cd Spider_XHS
pip install -r requirements.txt
```

#### 1.2 获取小红书Cookies
1. 打开浏览器，登录小红书网页版
2. 按F12打开开发者工具
3. 在Network标签页中找到任意请求
4. 复制Request Headers中的Cookie值

### 2. 创建监控账号

1. 访问系统Web界面：http://127.0.0.1:8888
2. 进入"账号管理"页面
3. 点击"添加账号"，填写以下信息：
   - **账号名称**：例如"小红书监控账号"
   - **平台**：选择"xiaohongshu"
   - **账号类型**：选择"monitor"
   - **Cookies**：粘贴从浏览器获取的Cookie值
   - **配置**：可留空

### 3. 创建监控任务

#### 3.1 通过Web界面创建
1. 访问"小红书监控"页面：http://127.0.0.1:8888/xiaohongshu
2. 点击"创建监控任务"
3. 填写任务信息：
   - **任务名称**：例如"监控用户A的视频"
   - **用户ID**：目标用户的ID（如：565e638bcb35fb1b0c287ac7）
   - **监控账号**：选择已创建的监控账号
   - **目标账号**：选择上传目标账号（可选）
   - **检查间隔**：建议3600秒（1小时）
   - **下载数量限制**：建议10个
   - **排除置顶作品**：勾选（推荐）
   - **只获取视频作品**：勾选（推荐）

#### 3.2 通过API创建
```bash
curl -X POST http://127.0.0.1:8888/api/tasks/xiaohongshu/create \
  -H "Content-Type: application/json" \
  -d '{
    "name": "监控用户视频",
    "user_id": "565e638bcb35fb1b0c287ac7",
    "monitor_account_id": "your_monitor_account_id",
    "target_account_id": "your_target_account_id",
    "check_interval": 3600,
    "download_limit": 10,
    "exclude_pinned": true,
    "video_only": true,
    "auto_upload": true
  }'
```

### 4. 测试监控功能

在创建正式任务前，建议先测试监控功能：

1. 在"小红书监控"页面点击"测试监控"
2. 填写测试参数：
   - **用户ID**：565e638bcb35fb1b0c287ac7（示例用户）
   - **监控账号**：选择监控账号
   - **获取数量**：5（测试用）
   - **排除置顶**：勾选
   - **只要视频**：勾选
3. 点击"开始测试"查看结果

### 5. 启动和管理任务

1. **启动任务**：在任务列表中点击"播放"按钮
2. **停止任务**：点击"暂停"按钮
3. **查看状态**：任务状态会实时更新
4. **查看日志**：在"日志查看"页面查看详细执行日志

## 配置说明

### 任务配置参数

| 参数 | 说明 | 默认值 | 推荐值 |
|------|------|--------|--------|
| `exclude_pinned` | 排除置顶作品 | true | true |
| `video_only` | 只获取视频作品 | true | true |
| `download_limit` | 每次获取数量限制 | 10 | 10-20 |
| `check_interval` | 检查间隔（秒） | 3600 | 3600-7200 |
| `auto_upload` | 自动上传到目标平台 | true | 根据需要 |

### 监控逻辑详解

根据您的示例输出，系统的处理逻辑如下：

```
用户 565e638bcb35fb1b0c287ac7 作品数量: 12
├── 置顶作品 (2个) - 被排除
│   ├── 是否置顶：True, 媒体类型：video
│   └── 是否置顶：True, 媒体类型：video
└── 非置顶作品 (10个)
    ├── 是否置顶：False, 媒体类型：video ✅ 符合条件
    ├── 是否置顶：False, 媒体类型：video ✅ 符合条件
    ├── 是否置顶：False, 媒体类型：normal ❌ 非视频，排除
    └── ... 其他作品
```

最终只有**非置顶的视频作品**会被下载。

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: 小红书API模块未找到
   ```
   **解决方案**：检查Spider_XHS模块是否正确安装和配置

2. **Cookies失效**
   ```
   获取笔记信息失败: 登录状态失效
   ```
   **解决方案**：重新获取并更新账号的Cookies

3. **用户ID错误**
   ```
   获取笔记信息失败: 用户不存在
   ```
   **解决方案**：检查用户ID是否正确，可以从用户主页URL中获取

4. **下载失败**
   ```
   下载内容失败: 网络连接超时
   ```
   **解决方案**：检查网络连接，可能需要配置代理

### 调试方法

1. **查看日志**：在Web界面的"日志查看"页面查看详细日志
2. **测试功能**：使用"测试监控"功能验证配置是否正确
3. **手动测试**：运行测试脚本进行调试
   ```bash
   python test_xiaohongshu_monitor.py
   ```

## API接口

### 创建监控任务
```http
POST /api/tasks/xiaohongshu/create
Content-Type: application/json

{
  "name": "任务名称",
  "user_id": "小红书用户ID",
  "monitor_account_id": "监控账号ID",
  "target_account_id": "目标账号ID",
  "check_interval": 3600,
  "download_limit": 10,
  "exclude_pinned": true,
  "video_only": true,
  "auto_upload": true
}
```

### 测试监控功能
```http
POST /api/tasks/xiaohongshu/test
Content-Type: application/json

{
  "user_id": "565e638bcb35fb1b0c287ac7",
  "account_id": "监控账号ID",
  "limit": 5,
  "exclude_pinned": true,
  "video_only": true
}
```

## 注意事项

1. **频率限制**：建议检查间隔不少于1小时，避免被平台限制
2. **Cookies管理**：定期检查和更新Cookies，确保监控正常运行
3. **存储空间**：确保有足够的磁盘空间存储下载的视频文件
4. **网络稳定**：保持网络连接稳定，避免下载中断
5. **合规使用**：请遵守平台的使用条款和相关法律法规

## 更新日志

- **v1.0.0**：初始版本，支持基本的小红书视频监控功能
- **v1.1.0**：增加了置顶作品过滤和视频类型筛选功能
- **v1.2.0**：集成了Web界面管理功能
