"""
文件工具模块
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import Optional, List, Dict, Any
from urllib.parse import urlparse
import requests

from .logger import get_logger
from .config import config

logger = get_logger(__name__)


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def ensure_dir(path: str) -> str:
        """确保目录存在"""
        Path(path).mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def get_file_hash(file_path: str, algorithm: str = "md5") -> Optional[str]:
        """获取文件哈希值"""
        if not os.path.exists(file_path):
            return None
        
        hash_obj = hashlib.new(algorithm)
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {file_path}, 错误: {e}")
            return None
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """获取文件大小"""
        try:
            return os.path.getsize(file_path)
        except OSError:
            return 0
    
    @staticmethod
    def get_safe_filename(filename: str) -> str:
        """获取安全的文件名"""
        # 移除或替换不安全的字符
        unsafe_chars = '<>:"/\\|?*'
        safe_filename = filename
        for char in unsafe_chars:
            safe_filename = safe_filename.replace(char, '_')
        
        # 限制长度
        if len(safe_filename) > 200:
            name, ext = os.path.splitext(safe_filename)
            safe_filename = name[:200-len(ext)] + ext
        
        return safe_filename
    
    @staticmethod
    def get_unique_filename(directory: str, filename: str) -> str:
        """获取唯一的文件名"""
        base_path = os.path.join(directory, filename)
        if not os.path.exists(base_path):
            return base_path
        
        name, ext = os.path.splitext(filename)
        counter = 1
        
        while True:
            new_filename = f"{name}_{counter}{ext}"
            new_path = os.path.join(directory, new_filename)
            if not os.path.exists(new_path):
                return new_path
            counter += 1
    
    @staticmethod
    def download_file(url: str,
                     save_path: str,
                     headers: Optional[Dict[str, str]] = None,
                     timeout: int = 300,
                     max_retries: int = 3) -> bool:
        """下载文件"""
        import time

        for attempt in range(max_retries):
            try:
                # 确保保存目录存在
                FileUtils.ensure_dir(os.path.dirname(save_path))

                # 设置默认请求头
                default_headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                if headers:
                    default_headers.update(headers)

                # 获取代理配置
                proxy_config = config.get_proxy_config()
                proxies = {}
                if proxy_config['http']:
                    proxies['http'] = proxy_config['http']
                if proxy_config['https']:
                    proxies['https'] = proxy_config['https']

                logger.info(f"开始下载文件 (尝试 {attempt + 1}/{max_retries}): {url}")

                with requests.get(url, headers=default_headers, proxies=proxies,
                                timeout=timeout, stream=True) as response:
                    response.raise_for_status()

                    # 获取文件大小
                    total_size = int(response.headers.get('content-length', 0))
                    downloaded_size = 0

                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                downloaded_size += len(chunk)

                                # 显示进度
                                if total_size > 0:
                                    progress = (downloaded_size / total_size) * 100
                                    if downloaded_size % (1024 * 1024) == 0:  # 每MB显示一次
                                        logger.debug(f"下载进度: {progress:.1f}%")

                logger.info(f"文件下载完成: {save_path}")
                return True

            except Exception as e:
                logger.error(f"下载文件失败 (尝试 {attempt + 1}/{max_retries}): {url}, 错误: {e}")
                # 删除不完整的文件
                if os.path.exists(save_path):
                    os.remove(save_path)

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    # 最后一次尝试失败，抛出异常
                    raise
    
    @staticmethod
    def move_file(src_path: str, dst_path: str) -> bool:
        """移动文件"""
        try:
            # 确保目标目录存在
            FileUtils.ensure_dir(os.path.dirname(dst_path))
            
            shutil.move(src_path, dst_path)
            logger.info(f"文件移动成功: {src_path} -> {dst_path}")
            return True
        except Exception as e:
            logger.error(f"文件移动失败: {src_path} -> {dst_path}, 错误: {e}")
            return False
    
    @staticmethod
    def copy_file(src_path: str, dst_path: str) -> bool:
        """复制文件"""
        try:
            # 确保目标目录存在
            FileUtils.ensure_dir(os.path.dirname(dst_path))
            
            shutil.copy2(src_path, dst_path)
            logger.info(f"文件复制成功: {src_path} -> {dst_path}")
            return True
        except Exception as e:
            logger.error(f"文件复制失败: {src_path} -> {dst_path}, 错误: {e}")
            return False
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """删除文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"文件删除成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"文件删除失败: {file_path}, 错误: {e}")
            return False
    
    @staticmethod
    def clean_directory(directory: str, max_age_days: int = 7) -> int:
        """清理目录中的旧文件"""
        if not os.path.exists(directory):
            return 0
        
        import time
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 3600
        deleted_count = 0
        
        try:
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    file_age = current_time - os.path.getmtime(file_path)
                    if file_age > max_age_seconds:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.debug(f"删除旧文件: {file_path}")
            
            logger.info(f"清理目录完成: {directory}, 删除文件数: {deleted_count}")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理目录失败: {directory}, 错误: {e}")
            return 0
    
    @staticmethod
    def get_directory_size(directory: str) -> int:
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
        except Exception as e:
            logger.error(f"计算目录大小失败: {directory}, 错误: {e}")
        
        return total_size
