"""
配置管理模块
"""

import os
from typing import Any, Optional, Dict
from dotenv import load_dotenv
from pathlib import Path

from .logger import get_logger

logger = get_logger(__name__)


class Config:
    """配置管理类"""
    
    def __init__(self, env_file: str = ".env"):
        self.env_file = env_file
        self._load_env()
        self._config_cache = {}
    
    def _load_env(self):
        """加载环境变量"""
        if os.path.exists(self.env_file):
            load_dotenv(self.env_file)
            logger.info(f"已加载配置文件: {self.env_file}")
        else:
            logger.warning(f"配置文件不存在: {self.env_file}")
    
    def get(self, key: str, default: Any = None, cast_type: type = str) -> Any:
        """获取配置值"""
        # 先从缓存获取
        if key in self._config_cache:
            return self._config_cache[key]
        
        # 从环境变量获取
        value = os.getenv(key, default)
        
        # 类型转换
        if value is not None and cast_type != str:
            try:
                if cast_type == bool:
                    value = str(value).lower() in ('true', '1', 'yes', 'on')
                elif cast_type == int:
                    value = int(value)
                elif cast_type == float:
                    value = float(value)
                elif cast_type == list:
                    value = [item.strip() for item in str(value).split(',') if item.strip()]
            except (ValueError, TypeError) as e:
                logger.warning(f"配置值类型转换失败: {key}={value}, 错误: {e}")
                value = default
        
        # 缓存结果
        self._config_cache[key] = value
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        self._config_cache[key] = value
        os.environ[key] = str(value)
    
    def get_database_url(self) -> str:
        """获取数据库URL"""
        return self.get("DATABASE_URL", "sqlite:///data/database.db")
    
    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            "log_level": self.get("LOG_LEVEL", "INFO"),
            "log_file": self.get("LOG_FILE", "data/logs/app.log"),
            "max_file_size": self.get("LOG_MAX_SIZE", "10 MB"),
            "retention": self.get("LOG_RETENTION", "7 days")
        }
    
    def get_download_config(self) -> Dict[str, Any]:
        """获取下载配置"""
        return {
            "download_path": self.get("DOWNLOAD_PATH", "data/downloads"),
            "max_retries": self.get("MAX_DOWNLOAD_RETRIES", 3, int),
            "timeout": self.get("DOWNLOAD_TIMEOUT", 300, int),
            "max_concurrent": self.get("MAX_CONCURRENT_DOWNLOADS", 3, int)
        }
    
    def get_upload_config(self) -> Dict[str, Any]:
        """获取上传配置"""
        return {
            "upload_path": self.get("UPLOAD_PATH", "data/uploads"),
            "max_retries": self.get("MAX_UPLOAD_RETRIES", 3, int),
            "timeout": self.get("UPLOAD_TIMEOUT", 600, int),
            "max_concurrent": self.get("MAX_CONCURRENT_UPLOADS", 2, int)
        }
    
    def get_scheduler_config(self) -> Dict[str, Any]:
        """获取调度器配置"""
        return {
            "timezone": self.get("SCHEDULER_TIMEZONE", "Asia/Shanghai"),
            "default_interval": self.get("DEFAULT_CHECK_INTERVAL", 3600, int)
        }
    
    def get_platform_config(self, platform: str) -> Dict[str, Any]:
        """获取平台配置"""
        platform_upper = platform.upper()
        
        config = {
            "cookies": self.get(f"{platform_upper}_COOKIES", ""),
            "user_agent": self.get(f"{platform_upper}_USER_AGENT", 
                                 "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"),
        }
        
        # 平台特定配置
        if platform.lower() == "xiaohongshu":
            config.update({
                "a1": self.get("XHS_A1", ""),
                "web_session": self.get("XHS_WEB_SESSION", "")
            })
        elif platform.lower() == "youtube":
            config.update({
                "client_secrets_file": self.get("YOUTUBE_CLIENT_SECRETS_FILE", 
                                               "data/configs/youtube_client_secrets.json"),
                "oauth_file": self.get("YOUTUBE_OAUTH_FILE", 
                                     "data/configs/youtube_oauth.json")
            })
        elif platform.lower() == "bilibili":
            config.update({
                "csrf": self.get("BILIBILI_CSRF", "")
            })
        
        return config
    
    def get_proxy_config(self) -> Dict[str, Optional[str]]:
        """获取代理配置"""
        return {
            "http": self.get("HTTP_PROXY"),
            "https": self.get("HTTPS_PROXY")
        }
    
    def is_debug(self) -> bool:
        """是否调试模式"""
        return self.get("DEBUG", False, bool)
    
    def get_encrypt_key(self) -> str:
        """获取加密密钥"""
        key = self.get("ENCRYPT_KEY")
        if not key:
            logger.warning("未设置加密密钥，将使用默认密钥")
            return "default_encrypt_key_change_me"
        return key
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.get("DOWNLOAD_PATH", "data/downloads"),
            self.get("UPLOAD_PATH", "data/uploads"),
            os.path.dirname(self.get("LOG_FILE", "data/logs/app.log")),
            "data/configs"
        ]
        
        for directory in directories:
            if directory:
                Path(directory).mkdir(parents=True, exist_ok=True)
                logger.debug(f"确保目录存在: {directory}")


# 全局配置实例
config = Config()
