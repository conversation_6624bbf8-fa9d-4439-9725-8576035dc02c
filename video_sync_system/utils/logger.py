"""
日志工具模块
"""

import os
import sys
from loguru import logger
from typing import Optional


class LoggerConfig:
    """日志配置类"""
    
    def __init__(self, 
                 log_level: str = "INFO",
                 log_file: Optional[str] = None,
                 max_file_size: str = "10 MB",
                 retention: str = "7 days"):
        self.log_level = log_level
        self.log_file = log_file
        self.max_file_size = max_file_size
        self.retention = retention
    
    def setup(self):
        """设置日志配置"""
        # 移除默认处理器
        logger.remove()
        
        # 控制台输出格式
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        
        # 文件输出格式
        file_format = (
            "{time:YYYY-MM-DD HH:mm:ss} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )
        
        # 添加控制台处理器
        logger.add(
            sys.stdout,
            format=console_format,
            level=self.log_level,
            colorize=True
        )
        
        # 添加文件处理器
        if self.log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            logger.add(
                self.log_file,
                format=file_format,
                level=self.log_level,
                rotation=self.max_file_size,
                retention=self.retention,
                compression="zip",
                encoding="utf-8"
            )


# 全局日志配置实例
_logger_config = None


def setup_logger(log_level: str = "INFO", 
                log_file: Optional[str] = None,
                max_file_size: str = "10 MB",
                retention: str = "7 days"):
    """设置全局日志配置"""
    global _logger_config
    _logger_config = LoggerConfig(log_level, log_file, max_file_size, retention)
    _logger_config.setup()


def get_logger(name: str = None):
    """获取日志记录器"""
    if _logger_config is None:
        # 使用默认配置
        setup_logger()
    
    if name:
        return logger.bind(name=name)
    return logger


# 便捷函数
def log_info(message: str, **kwargs):
    """记录信息日志"""
    get_logger().info(message, **kwargs)


def log_error(message: str, **kwargs):
    """记录错误日志"""
    get_logger().error(message, **kwargs)


def log_warning(message: str, **kwargs):
    """记录警告日志"""
    get_logger().warning(message, **kwargs)


def log_debug(message: str, **kwargs):
    """记录调试日志"""
    get_logger().debug(message, **kwargs)


def log_exception(message: str = "发生异常", **kwargs):
    """记录异常日志"""
    get_logger().exception(message, **kwargs)
