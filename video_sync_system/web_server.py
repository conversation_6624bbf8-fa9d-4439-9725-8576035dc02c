#!/usr/bin/env python3
"""
Web服务器模块
提供Web界面和RESTful API
"""

import os
import sys
import json
import asyncio
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import threading

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.task_manager import TaskManager
from core.scheduler import TaskScheduler
from core.database import Database
from models.task import Task, TaskStatus
from models.account import Account, PlatformType, AccountType
from utils.logger import get_logger
from utils.config import config

logger = get_logger(__name__)

class WebServer:
    """Web服务器类"""

    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.app = Flask(__name__,
                        template_folder='web/templates',
                        static_folder='web/static')
        self.app.config['SECRET_KEY'] = 'video_sync_secret_key'

        # 启用CORS
        CORS(self.app)

        # 启用WebSocket
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='threading')

        # 初始化组件
        self.db = Database()
        self.task_manager = TaskManager()
        self.scheduler = None

        # 记录启动时间
        self.start_time = time.time()

        # 设置路由
        self._setup_routes()
        self._setup_websocket_handlers()
    
    def _setup_routes(self):
        """设置路由"""
        
        # 静态文件路由
        @self.app.route('/')
        def index():
            return render_template('index.html')
        
        @self.app.route('/accounts')
        def accounts_page():
            return render_template('accounts.html')
        
        @self.app.route('/tasks')
        def tasks_page():
            return render_template('tasks.html')
        
        @self.app.route('/logs')
        def logs_page():
            return render_template('logs.html')
        
        # API路由
        
        # 系统状态API
        @self.app.route('/api/status')
        def get_system_status():
            try:
                accounts = self.db.get_all_accounts()
                tasks = self.db.get_all_tasks()
                
                running_tasks = [t for t in tasks if t.status == TaskStatus.RUNNING]
                
                status = {
                    'system_status': 'running',
                    'total_accounts': len(accounts),
                    'total_tasks': len(tasks),
                    'running_tasks': len(running_tasks),
                    'scheduler_status': 'running' if self.scheduler and self.scheduler.is_running() else 'stopped',
                    'uptime': self._get_uptime(),
                    'timestamp': datetime.now().isoformat()
                }
                
                return jsonify(status)
            except Exception as e:
                logger.error(f"获取系统状态失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        # 账号管理API
        @self.app.route('/api/accounts', methods=['GET'])
        def get_accounts():
            try:
                accounts = self.db.get_all_accounts()
                accounts_data = []
                
                for account in accounts:
                    account_data = {
                        'id': account.id,
                        'name': account.name,
                        'platform': account.platform.value,
                        'account_type': account.account_type.value,
                        'is_active': account.is_active,
                        'created_at': account.created_at.isoformat() if account.created_at else None,
                        'last_used': account.last_used.isoformat() if account.last_used else None
                    }
                    accounts_data.append(account_data)
                
                return jsonify(accounts_data)
            except Exception as e:
                logger.error(f"获取账号列表失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/accounts', methods=['POST'])
        def create_account():
            try:
                data = request.get_json()
                
                # 验证必需字段
                required_fields = ['name', 'platform', 'account_type', 'cookies']
                for field in required_fields:
                    if field not in data:
                        return jsonify({'error': f'缺少必需字段: {field}'}), 400
                
                # 创建账号对象
                account = Account(
                    name=data['name'],
                    platform=PlatformType(data['platform']),
                    account_type=AccountType(data['account_type']),
                    cookies=data['cookies'],
                    config=data.get('config', {})
                )
                
                # 保存到数据库
                account_id = self.db.create_account(account)
                if account_id:
                    account.id = account_id
                    logger.info(f"账号创建成功: {account.name}")
                    
                    # 广播更新
                    self.socketio.emit('account_created', {
                        'id': account_id,
                        'name': account.name,
                        'platform': account.platform.value
                    })
                    
                    return jsonify({'id': account_id, 'message': '账号创建成功'})
                else:
                    return jsonify({'error': '账号创建失败'}), 500
                    
            except Exception as e:
                logger.error(f"创建账号失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/accounts/<account_id>', methods=['DELETE'])
        def delete_account(account_id):
            try:
                success = self.db.delete_account(account_id)
                if success:
                    logger.info(f"账号删除成功: {account_id}")
                    
                    # 广播更新
                    self.socketio.emit('account_deleted', {'id': account_id})
                    
                    return jsonify({'message': '账号删除成功'})
                else:
                    return jsonify({'error': '账号删除失败'}), 500
            except Exception as e:
                logger.error(f"删除账号失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        # 任务管理API
        @self.app.route('/api/tasks', methods=['GET'])
        def get_tasks():
            try:
                tasks_data = asyncio.run(self.task_manager.list_tasks())
                return jsonify(tasks_data)
            except Exception as e:
                logger.error(f"获取任务列表失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/tasks', methods=['POST'])
        def create_task():
            try:
                data = request.get_json()
                
                # 验证必需字段
                required_fields = ['name', 'source_platform', 'target_platforms', 
                                 'source_user_id', 'monitor_account_id', 'upload_account_ids']
                for field in required_fields:
                    if field not in data:
                        return jsonify({'error': f'缺少必需字段: {field}'}), 400
                
                # 创建任务
                task = asyncio.run(self.task_manager.create_task(
                    name=data['name'],
                    source_platform=PlatformType(data['source_platform']),
                    target_platforms=[PlatformType(p) for p in data['target_platforms']],
                    source_user_id=data['source_user_id'],
                    monitor_account_id=data['monitor_account_id'],
                    upload_account_ids={PlatformType(k): v for k, v in data['upload_account_ids'].items()},
                    check_interval=data.get('check_interval', 3600),
                    download_limit=data.get('download_limit', 10),
                    **data.get('config', {})
                ))
                
                if task:
                    logger.info(f"任务创建成功: {task.name}")
                    
                    # 广播更新
                    self.socketio.emit('task_created', {
                        'id': task.id,
                        'name': task.name,
                        'status': task.status.value
                    })
                    
                    return jsonify({'id': task.id, 'message': '任务创建成功'})
                else:
                    return jsonify({'error': '任务创建失败'}), 500
                    
            except Exception as e:
                logger.error(f"创建任务失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/tasks/<task_id>/start', methods=['POST'])
        def start_task(task_id):
            try:
                success = asyncio.run(self.task_manager.start_task(task_id))
                if success:
                    logger.info(f"任务启动成功: {task_id}")
                    
                    # 广播更新
                    self.socketio.emit('task_started', {'id': task_id})
                    
                    return jsonify({'message': '任务启动成功'})
                else:
                    return jsonify({'error': '任务启动失败'}), 500
            except Exception as e:
                logger.error(f"启动任务失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/tasks/<task_id>/stop', methods=['POST'])
        def stop_task(task_id):
            try:
                success = asyncio.run(self.task_manager.stop_task(task_id))
                if success:
                    logger.info(f"任务停止成功: {task_id}")
                    
                    # 广播更新
                    self.socketio.emit('task_stopped', {'id': task_id})
                    
                    return jsonify({'message': '任务停止成功'})
                else:
                    return jsonify({'error': '任务停止失败'}), 500
            except Exception as e:
                logger.error(f"停止任务失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/tasks/<task_id>', methods=['DELETE'])
        def delete_task(task_id):
            try:
                success = asyncio.run(self.task_manager.delete_task(task_id))
                if success:
                    logger.info(f"任务删除成功: {task_id}")
                    
                    # 广播更新
                    self.socketio.emit('task_deleted', {'id': task_id})
                    
                    return jsonify({'message': '任务删除成功'})
                else:
                    return jsonify({'error': '任务删除失败'}), 500
            except Exception as e:
                logger.error(f"删除任务失败: {e}")
                return jsonify({'error': str(e)}), 500

        # 系统监控API
        @self.app.route('/api/system-metrics')
        def get_system_metrics():
            """获取系统资源使用情况"""
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)

                # 内存使用情况
                memory = psutil.virtual_memory()
                memory_percent = memory.percent

                # 磁盘使用情况
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100

                # 网络状态（简单检查）
                network_status = 'online'  # 可以实现更复杂的网络检测

                metrics = {
                    'cpu_percent': round(cpu_percent, 1),
                    'memory_percent': round(memory_percent, 1),
                    'disk_percent': round(disk_percent, 1),
                    'network_status': network_status,
                    'timestamp': datetime.now().isoformat()
                }

                return jsonify(metrics)
            except Exception as e:
                logger.error(f"获取系统指标失败: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/stats')
        def get_stats():
            """获取统计数据"""
            try:
                accounts = self.db.get_all_accounts()
                tasks = self.db.get_all_tasks()

                # 统计任务状态
                running_tasks = len([t for t in tasks if t.status == TaskStatus.RUNNING])
                active_accounts = len([a for a in accounts if a.is_active])

                # 计算成功率（示例数据）
                total_executions = sum(t.success_count + t.error_count for t in tasks)
                total_success = sum(t.success_count for t in tasks)
                success_rate = round((total_success / total_executions * 100) if total_executions > 0 else 0, 1)

                # 今日视频数量（示例数据）
                videos_today = sum(t.success_count for t in tasks if t.last_run and
                                 datetime.fromisoformat(t.last_run).date() == datetime.now().date())

                # 每日统计数据（示例数据）
                daily_stats = []
                for i in range(7):
                    date = datetime.now() - timedelta(days=i)
                    # 这里应该从数据库查询实际数据
                    daily_stats.append({
                        'date': date.strftime('%Y-%m-%d'),
                        'success_count': max(0, 10 - i * 2),
                        'failure_count': max(0, i)
                    })

                stats = {
                    'total_tasks': len(tasks),
                    'running_tasks': running_tasks,
                    'total_accounts': len(accounts),
                    'active_accounts': active_accounts,
                    'videos_today': videos_today,
                    'success_rate': success_rate,
                    'daily_stats': daily_stats
                }

                return jsonify(stats)
            except Exception as e:
                logger.error(f"获取统计数据失败: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/activities')
        def get_activities():
            """获取最近活动"""
            try:
                # 这里应该从日志或活动记录中获取数据
                # 目前返回示例数据
                activities = [
                    {
                        'type': 'task_start',
                        'title': '任务启动',
                        'description': '小红书美食博主同步任务已启动',
                        'timestamp': (datetime.now() - timedelta(minutes=5)).isoformat()
                    },
                    {
                        'type': 'video_download',
                        'title': '视频下载',
                        'description': '成功下载视频: 美食制作教程',
                        'timestamp': (datetime.now() - timedelta(minutes=10)).isoformat()
                    },
                    {
                        'type': 'video_upload',
                        'title': '视频上传',
                        'description': '视频已上传到TikTok',
                        'timestamp': (datetime.now() - timedelta(minutes=15)).isoformat()
                    },
                    {
                        'type': 'account_add',
                        'title': '账号添加',
                        'description': '新增B站上传账号',
                        'timestamp': (datetime.now() - timedelta(hours=1)).isoformat()
                    },
                    {
                        'type': 'system_start',
                        'title': '系统启动',
                        'description': '视频同步系统已启动',
                        'timestamp': (datetime.now() - timedelta(hours=2)).isoformat()
                    }
                ]

                return jsonify(activities)
            except Exception as e:
                logger.error(f"获取活动记录失败: {e}")
                return jsonify({'error': str(e)}), 500
    
    def _setup_websocket_handlers(self):
        """设置WebSocket处理器"""
        
        @self.socketio.on('connect')
        def handle_connect():
            logger.info(f"客户端连接: {request.sid}")
            emit('connected', {'message': '连接成功'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            logger.info(f"客户端断开: {request.sid}")
    
    def _get_uptime(self):
        """获取系统运行时间"""
        uptime_seconds = int(time.time() - self.start_time)

        days = uptime_seconds // 86400
        hours = (uptime_seconds % 86400) // 3600
        minutes = (uptime_seconds % 3600) // 60
        seconds = uptime_seconds % 60

        if days > 0:
            return f"{days}天 {hours}小时 {minutes}分钟"
        elif hours > 0:
            return f"{hours}小时 {minutes}分钟"
        elif minutes > 0:
            return f"{minutes}分钟 {seconds}秒"
        else:
            return f"{seconds}秒"
    
    def set_scheduler(self, scheduler: TaskScheduler):
        """设置调度器引用"""
        self.scheduler = scheduler
    
    def run(self, debug=False):
        """启动Web服务器"""
        logger.info(f"启动Web服务器: http://{self.host}:{self.port}")
        self.socketio.run(self.app, host=self.host, port=self.port, debug=debug)
    
    def stop(self):
        """停止Web服务器"""
        logger.info("停止Web服务器")
        # Flask-SocketIO没有直接的停止方法，通常通过信号处理


if __name__ == '__main__':
    web_server = WebServer()
    web_server.run(debug=True)
