"""
平台适配器基类
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass

from models.account import Account
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ContentItem:
    """内容项数据类"""
    id: str                          # 内容ID
    title: str                       # 标题
    description: str = ""            # 描述
    url: str = ""                   # 原始URL
    author: str = ""                # 作者
    author_id: str = ""             # 作者ID
    publish_time: Optional[datetime] = None  # 发布时间
    content_type: str = "video"     # 内容类型 (video, image, text)
    media_urls: List[str] = None    # 媒体文件URL列表
    thumbnail_url: str = ""         # 缩略图URL
    tags: List[str] = None          # 标签
    view_count: int = 0             # 观看数
    like_count: int = 0             # 点赞数
    comment_count: int = 0          # 评论数
    share_count: int = 0            # 分享数
    extra_data: Dict[str, Any] = None  # 额外数据
    
    def __post_init__(self):
        if self.media_urls is None:
            self.media_urls = []
        if self.tags is None:
            self.tags = []
        if self.extra_data is None:
            self.extra_data = {}


@dataclass
class UploadResult:
    """上传结果数据类"""
    success: bool                   # 是否成功
    content_id: str = ""           # 上传后的内容ID
    url: str = ""                  # 上传后的URL
    message: str = ""              # 结果消息
    error: str = ""                # 错误信息
    extra_data: Dict[str, Any] = None  # 额外数据
    
    def __post_init__(self):
        if self.extra_data is None:
            self.extra_data = {}


class BaseMonitor(ABC):
    """监控适配器基类"""
    
    def __init__(self, account: Account):
        self.account = account
        self.platform_name = account.platform.value
        self.logger = get_logger(f"{self.__class__.__name__}")
    
    @abstractmethod
    async def get_latest_content(self, 
                               user_id: str, 
                               limit: int = 10,
                               since: Optional[datetime] = None) -> List[ContentItem]:
        """
        获取用户最新内容
        
        Args:
            user_id: 用户ID
            limit: 获取数量限制
            since: 获取此时间之后的内容
            
        Returns:
            内容项列表
        """
        pass
    
    @abstractmethod
    async def download_content(self, 
                             content_item: ContentItem, 
                             save_dir: str) -> List[str]:
        """
        下载内容文件
        
        Args:
            content_item: 内容项
            save_dir: 保存目录
            
        Returns:
            下载的文件路径列表
        """
        pass
    
    async def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户信息字典
        """
        # 默认实现，子类可以重写
        return {"user_id": user_id, "platform": self.platform_name}
    
    def validate_account(self) -> bool:
        """
        验证账号有效性
        
        Returns:
            是否有效
        """
        # 基础验证
        if not self.account.can_monitor():
            self.logger.error(f"账号不支持监控: {self.account.name}")
            return False
        
        # 子类可以重写进行更详细的验证
        return True
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
    
    def get_cookies(self) -> Dict[str, str]:
        """获取Cookie"""
        if not self.account.cookies:
            return {}
        
        # 简单的cookie解析
        cookies = {}
        for item in self.account.cookies.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
        
        return cookies


class BaseUploader(ABC):
    """上传适配器基类"""
    
    def __init__(self, account: Account):
        self.account = account
        self.platform_name = account.platform.value
        self.logger = get_logger(f"{self.__class__.__name__}")
    
    @abstractmethod
    async def upload_content(self, 
                           file_paths: List[str],
                           title: str,
                           description: str = "",
                           tags: List[str] = None,
                           **kwargs) -> UploadResult:
        """
        上传内容
        
        Args:
            file_paths: 文件路径列表
            title: 标题
            description: 描述
            tags: 标签列表
            **kwargs: 其他参数
            
        Returns:
            上传结果
        """
        pass
    
    def validate_account(self) -> bool:
        """
        验证账号有效性
        
        Returns:
            是否有效
        """
        # 基础验证
        if not self.account.can_upload():
            self.logger.error(f"账号不支持上传: {self.account.name}")
            return False
        
        # 子类可以重写进行更详细的验证
        return True
    
    def validate_files(self, file_paths: List[str]) -> Tuple[bool, str]:
        """
        验证文件
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            (是否有效, 错误信息)
        """
        import os
        
        if not file_paths:
            return False, "文件列表为空"
        
        for file_path in file_paths:
            if not os.path.exists(file_path):
                return False, f"文件不存在: {file_path}"
            
            if os.path.getsize(file_path) == 0:
                return False, f"文件为空: {file_path}"
        
        return True, ""
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
    
    def get_cookies(self) -> Dict[str, str]:
        """获取Cookie"""
        if not self.account.cookies:
            return {}
        
        # 简单的cookie解析
        cookies = {}
        for item in self.account.cookies.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
        
        return cookies
    
    async def prepare_content(self, 
                            content_item: ContentItem, 
                            file_paths: List[str]) -> Dict[str, Any]:
        """
        准备上传内容
        
        Args:
            content_item: 原始内容项
            file_paths: 文件路径列表
            
        Returns:
            准备好的内容数据
        """
        # 默认实现，子类可以重写
        return {
            "title": content_item.title,
            "description": content_item.description,
            "tags": content_item.tags,
            "file_paths": file_paths
        }
