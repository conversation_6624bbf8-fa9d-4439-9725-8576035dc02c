"""
增强版小红书监控器
专门用于监控用户最新视频作品，排除置顶和非视频内容
"""

import os
import sys
import json
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urlparse

# 添加legacy路径以导入现有代码
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'Spider_XHS'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from .base import BaseMonitor, ContentItem
from models.account import Account
from utils.logger import get_logger

# 导入现有的小红书API
try:
    from Spider_XHS.apis.xhs_pc_apis import XHS_Apis
    from Spider_XHS.xhs_utils.data_util import handle_note_info, download_note
    from Spider_XHS.xhs_utils.common_util import init
except ImportError as e:
    print(f"导入小红书模块失败: {e}")
    XHS_Apis = None

logger = get_logger(__name__)


class XiaohongshuEnhancedMonitor(BaseMonitor):
    """增强版小红书监控器 - 专门监控最新视频作品"""
    
    def __init__(self, account: Account):
        super().__init__(account)
        if XHS_Apis is None:
            raise ImportError("小红书API模块未找到，请检查Spider_XHS路径")
        
        self.xhs_api = XHS_Apis()
        self.cookies_str = account.cookies
        self.logger = logger
    
    async def monitor_user_latest_videos(self, 
                                       user_id: str, 
                                       limit: int = 10,
                                       exclude_pinned: bool = True,
                                       video_only: bool = True) -> List[ContentItem]:
        """
        监控用户最新视频作品
        
        Args:
            user_id: 用户ID
            limit: 获取数量限制（实际获取的非置顶视频数量）
            exclude_pinned: 是否排除置顶作品
            video_only: 是否只获取视频作品
            
        Returns:
            List[ContentItem]: 符合条件的内容列表
        """
        try:
            self.logger.info(f"开始监控用户 {user_id} 的最新视频作品")
            
            # 获取用户笔记信息
            success, msg, note_info = self.xhs_api.get_user_note_info(user_id, "", self.cookies_str)
            
            if not success or note_info is None or 'data' not in note_info:
                self.logger.error(f"获取笔记信息失败: {msg}")
                return []
            
            if 'notes' not in note_info.get('data', {}):
                self.logger.error("笔记信息中没有notes字段")
                return []
            
            notes = note_info.get("data").get("notes")
            self.logger.info(f'用户 {user_id} 总作品数量: {len(notes)}')
            
            # 筛选符合条件的笔记
            filtered_notes = []
            pinned_count = 0
            non_video_count = 0
            
            for simple_note_info in notes:
                try:
                    # 检查是否置顶
                    interact_info = simple_note_info.get("interact_info", {})
                    is_pinned = interact_info.get("sticky", False) if interact_info else False
                    
                    # 检查媒体类型
                    media_type = simple_note_info.get("type", "")
                    is_video = media_type == "video"
                    
                    self.logger.debug(f"笔记 {simple_note_info.get('note_id', 'unknown')}: "
                                    f"置顶={is_pinned}, 类型={media_type}")
                    
                    # 统计置顶和非视频数量
                    if is_pinned:
                        pinned_count += 1
                    if not is_video:
                        non_video_count += 1
                    
                    # 应用过滤条件
                    if exclude_pinned and is_pinned:
                        self.logger.debug(f"跳过置顶作品: {simple_note_info.get('note_id')}")
                        continue
                    
                    if video_only and not is_video:
                        self.logger.debug(f"跳过非视频作品: {simple_note_info.get('note_id')}")
                        continue
                    
                    # 构造笔记URL
                    note_id = simple_note_info.get('note_id')
                    xsec_token = simple_note_info.get('xsec_token', '')
                    
                    if note_id:
                        note_url = f"https://www.xiaohongshu.com/explore/{note_id}?xsec_token={xsec_token}"
                        filtered_notes.append({
                            'note_id': note_id,
                            'note_url': note_url,
                            'xsec_token': xsec_token,
                            'is_pinned': is_pinned,
                            'media_type': media_type,
                            'simple_info': simple_note_info
                        })
                        
                        self.logger.info(f"符合条件的作品: {note_url}")
                        
                        # 达到限制数量就停止
                        if len(filtered_notes) >= limit:
                            break
                    else:
                        self.logger.warning("笔记ID不存在")
                        
                except Exception as e:
                    self.logger.error(f"处理笔记信息出错: {str(e)}")
                    continue
            
            self.logger.info(f"筛选结果: 总作品={len(notes)}, 置顶={pinned_count}, "
                           f"非视频={non_video_count}, 符合条件={len(filtered_notes)}")
            
            # 转换为ContentItem列表
            content_items = []
            for note_data in filtered_notes:
                try:
                    content_item = await self._convert_note_to_content_item(note_data)
                    if content_item:
                        content_items.append(content_item)
                except Exception as e:
                    self.logger.error(f"转换笔记失败: {note_data['note_id']}, 错误: {e}")
                    continue
            
            self.logger.info(f"成功获取 {len(content_items)} 条符合条件的视频作品")
            return content_items
            
        except Exception as e:
            self.logger.error(f"监控用户最新视频失败: {e}")
            return []
    
    async def _convert_note_to_content_item(self, note_data: Dict[str, Any]) -> Optional[ContentItem]:
        """将笔记数据转换为ContentItem"""
        try:
            note_url = note_data['note_url']
            simple_info = note_data['simple_info']
            
            # 获取详细笔记信息（可选，如果需要更多详情）
            # success, msg, detail_info = self.xhs_api.get_note_info(note_url, self.cookies_str)
            
            # 从简单信息中提取基本数据
            return ContentItem(
                id=note_data['note_id'],
                title=simple_info.get('display_title', ''),
                description='',  # 简单信息中没有描述
                url=note_url,
                author='',  # 简单信息中没有作者信息
                author_id='',
                publish_time=None,  # 需要详细信息才能获取
                content_type='video' if note_data['media_type'] == 'video' else 'image',
                media_urls=[],  # 需要详细信息才能获取
                thumbnail_url=simple_info.get('cover', {}).get('url', '') if simple_info.get('cover') else '',
                tags=[],
                view_count=0,
                like_count=0,
                comment_count=0,
                share_count=0,
                extra_data={
                    'note_id': note_data['note_id'],
                    'xsec_token': note_data['xsec_token'],
                    'is_pinned': note_data['is_pinned'],
                    'media_type': note_data['media_type'],
                    'simple_info': simple_info
                }
            )
            
        except Exception as e:
            self.logger.error(f"转换笔记数据失败: {e}")
            return None
    
    async def download_filtered_videos(self, 
                                     content_items: List[ContentItem], 
                                     save_dir: str,
                                     cookies_str: str = None) -> List[str]:
        """
        下载筛选出的视频作品
        使用spider_some_note方法进行下载
        
        Args:
            content_items: 要下载的内容列表
            save_dir: 保存目录
            cookies_str: cookies字符串，如果不提供则使用账号的cookies
            
        Returns:
            List[str]: 下载成功的文件路径列表
        """
        try:
            if not content_items:
                self.logger.warning("没有要下载的内容")
                return []
            
            # 使用提供的cookies或账号cookies
            cookies = cookies_str or self.cookies_str
            if not cookies:
                self.logger.error("没有可用的cookies")
                return []
            
            # 准备笔记URL列表
            note_urls = [item.url for item in content_items]
            self.logger.info(f"准备下载 {len(note_urls)} 个视频作品")
            
            # 确保保存目录存在
            os.makedirs(save_dir, exist_ok=True)
            
            # 准备base_path字典
            base_path = {
                'media': save_dir,
                'excel': save_dir
            }
            
            # 调用spider_some_note进行下载
            downloaded_files = await self._spider_some_note(
                notes=note_urls,
                cookies_str=cookies,
                base_path=base_path,
                save_choice='media'  # 只下载媒体文件
            )
            
            self.logger.info(f"下载完成，共 {len(downloaded_files)} 个文件")
            return downloaded_files
            
        except Exception as e:
            self.logger.error(f"下载视频失败: {e}")
            return []
    
    async def _spider_some_note(self, 
                              notes: List[str], 
                              cookies_str: str, 
                              base_path: Dict[str, str], 
                              save_choice: str) -> List[str]:
        """
        爬取笔记数据（异步版本的spider_some_note）
        """
        downloaded_files = []
        
        try:
            for note_url in notes:
                try:
                    self.logger.info(f"开始下载: {note_url}")
                    
                    # 获取笔记详细信息
                    success, msg, note_info = self.xhs_api.spider_note(note_url, cookies_str)
                    
                    if not success or not note_info:
                        self.logger.warning(f"获取笔记信息失败: {note_url}, {msg}")
                        continue
                    
                    # 下载媒体文件
                    if save_choice in ['all', 'media']:
                        try:
                            file_paths = download_note(note_info, base_path['media'])
                            if file_paths:
                                if isinstance(file_paths, list):
                                    downloaded_files.extend(file_paths)
                                else:
                                    downloaded_files.append(file_paths)
                                self.logger.info(f"下载成功: {note_url}")
                            else:
                                self.logger.warning(f"下载失败: {note_url}")
                        except Exception as e:
                            self.logger.error(f"下载文件失败: {note_url}, 错误: {e}")
                    
                except Exception as e:
                    self.logger.error(f"处理笔记失败: {note_url}, 错误: {e}")
                    continue
            
            return downloaded_files
            
        except Exception as e:
            self.logger.error(f"批量下载失败: {e}")
            return downloaded_files
    
    async def get_latest_content(self, 
                               user_id: str, 
                               limit: int = 10,
                               since: Optional[datetime] = None) -> List[ContentItem]:
        """
        实现基类的抽象方法
        获取最新内容（排除置顶，只要视频）
        """
        return await self.monitor_user_latest_videos(
            user_id=user_id,
            limit=limit,
            exclude_pinned=True,
            video_only=True
        )
    
    async def download_content(self, 
                             content_item: ContentItem, 
                             save_dir: str) -> List[str]:
        """
        实现基类的抽象方法
        下载单个内容
        """
        return await self.download_filtered_videos([content_item], save_dir)
