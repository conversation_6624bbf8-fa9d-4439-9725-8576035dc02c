"""
小红书平台适配器
"""

import os
import sys
import json
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse

# 添加legacy路径以导入现有代码
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'Spider_XHS'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from .base import BaseMonitor, BaseUploader, ContentItem, UploadResult
from models.account import Account
from utils.logger import get_logger
from utils.file_utils import FileUtils

# 导入现有的小红书API
try:
    from Spider_XHS.apis.xhs_pc_apis import XHS_Apis
    from Spider_XHS.xhs_utils.data_util import handle_note_info, download_note
    from xhs import XhsClient
except ImportError as e:
    print(f"导入小红书模块失败: {e}")
    XHS_Apis = None
    XhsClient = None

logger = get_logger(__name__)


class XiaoHongShuMonitor(BaseMonitor):
    """小红书监控适配器"""
    
    def __init__(self, account: Account):
        super().__init__(account)
        if XHS_Apis is None:
            raise ImportError("小红书API模块未找到，请检查legacy代码路径")
        
        self.xhs_api = XHS_Apis()
        self.cookies_str = account.cookies
    
    async def get_latest_content(self, 
                               user_id: str, 
                               limit: int = 10,
                               since: Optional[datetime] = None) -> List[ContentItem]:
        """获取用户最新内容"""
        try:
            # 构造用户URL
            user_url = f"https://www.xiaohongshu.com/user/profile/{user_id}"
            
            # 获取用户所有笔记
            success, msg, note_list = self.xhs_api.get_user_all_notes(user_url, self.cookies_str)
            
            if not success:
                self.logger.error(f"获取用户笔记失败: {msg}")
                return []
            
            content_items = []
            processed_count = 0
            
            for note_info in note_list:
                if processed_count >= limit:
                    break
                
                try:
                    # 获取详细笔记信息
                    note_url = f"https://www.xiaohongshu.com/explore/{note_info['note_id']}?xsec_token={note_info.get('xsec_token', '')}"
                    success, msg, detail_info = self.xhs_api.get_note_info(note_url, self.cookies_str)
                    
                    if not success:
                        self.logger.warning(f"获取笔记详情失败: {note_info['note_id']}, {msg}")
                        continue
                    
                    # 处理笔记信息
                    processed_note = handle_note_info(detail_info['data']['items'][0])
                    
                    # 检查时间过滤
                    if since:
                        note_time = datetime.fromtimestamp(processed_note.get('timestamp', 0))
                        if note_time <= since:
                            continue
                    
                    # 转换为ContentItem
                    content_item = self._convert_to_content_item(processed_note)
                    content_items.append(content_item)
                    processed_count += 1
                    
                except Exception as e:
                    self.logger.error(f"处理笔记失败: {note_info.get('note_id', 'unknown')}, 错误: {e}")
                    continue
            
            self.logger.info(f"获取到 {len(content_items)} 条内容")
            return content_items
            
        except Exception as e:
            self.logger.error(f"获取最新内容失败: {e}")
            return []
    
    async def download_content(self, 
                             content_item: ContentItem, 
                             save_dir: str) -> List[str]:
        """下载内容文件"""
        try:
            FileUtils.ensure_dir(save_dir)
            
            # 构造笔记信息用于下载
            note_info = {
                'note_id': content_item.id,
                'user_id': content_item.author_id,
                'title': content_item.title,
                'nickname': content_item.author,
                'note_type': '视频' if content_item.content_type == 'video' else '图集',
                'image_list': content_item.media_urls,
                'video_cover': content_item.thumbnail_url,
                'video_addr': content_item.media_urls[0] if content_item.media_urls else '',
            }
            
            # 使用现有的下载函数
            save_path = download_note(note_info, save_dir, 'media')
            
            # 收集下载的文件路径
            downloaded_files = []
            if os.path.exists(save_path):
                for file_name in os.listdir(save_path):
                    if file_name.endswith(('.jpg', '.png', '.mp4')):
                        downloaded_files.append(os.path.join(save_path, file_name))
            
            self.logger.info(f"下载完成: {len(downloaded_files)} 个文件")
            return downloaded_files
            
        except Exception as e:
            self.logger.error(f"下载内容失败: {e}")
            return []
    
    def _convert_to_content_item(self, note_info: Dict[str, Any]) -> ContentItem:
        """将笔记信息转换为ContentItem"""
        media_urls = []
        content_type = "image"
        
        if note_info.get('note_type') == '视频':
            content_type = "video"
            if note_info.get('video_addr'):
                media_urls.append(note_info['video_addr'])
        else:
            if note_info.get('image_list'):
                media_urls.extend(note_info['image_list'])
        
        # 解析发布时间
        publish_time = None
        if note_info.get('upload_time'):
            try:
                publish_time = datetime.strptime(note_info['upload_time'], '%Y-%m-%d %H:%M:%S')
            except:
                pass
        
        return ContentItem(
            id=note_info.get('note_id', ''),
            title=note_info.get('title', ''),
            description=note_info.get('desc', ''),
            url=note_info.get('note_url', ''),
            author=note_info.get('nickname', ''),
            author_id=note_info.get('user_id', ''),
            publish_time=publish_time,
            content_type=content_type,
            media_urls=media_urls,
            thumbnail_url=note_info.get('video_cover', ''),
            tags=note_info.get('tags', []),
            view_count=0,  # 小红书不提供观看数
            like_count=note_info.get('liked_count', 0),
            comment_count=note_info.get('comment_count', 0),
            share_count=note_info.get('share_count', 0),
            extra_data=note_info
        )


class XiaoHongShuUploader(BaseUploader):
    """小红书上传适配器"""
    
    def __init__(self, account: Account):
        super().__init__(account)
        if XhsClient is None:
            raise ImportError("小红书上传模块未找到，请检查xhs模块")
        
        # 从账号配置中获取必要信息
        config = account.config or {}
        self.a1 = config.get('a1', '')
        self.web_session = config.get('web_session', '')
        
        # 初始化客户端
        self.xhs_client = XhsClient(account.cookies, sign=self._sign)
    
    def _sign(self, uri, data=None):
        """签名函数"""
        try:
            from playwright.sync_api import sync_playwright
            import time
            
            for _ in range(3):  # 重试3次
                try:
                    with sync_playwright() as playwright:
                        stealth_js_path = os.path.join(
                            os.path.dirname(__file__), '..', '..', 'xhs_upload_js', 'stealth.min.js'
                        )
                        
                        chromium = playwright.chromium
                        browser = chromium.launch(headless=True)
                        browser_context = browser.new_context()
                        
                        if os.path.exists(stealth_js_path):
                            browser_context.add_init_script(path=stealth_js_path)
                        
                        context_page = browser_context.new_page()
                        context_page.goto("https://www.xiaohongshu.com")
                        
                        if self.a1:
                            browser_context.add_cookies([
                                {'name': 'a1', 'value': self.a1, 'domain': ".xiaohongshu.com", 'path': "/"}
                            ])
                            context_page.reload()
                        
                        time.sleep(3)  # 等待页面加载
                        
                        encrypt_params = context_page.evaluate(
                            "([url, data]) => window._webmsxyw(url, data)", 
                            [uri, data]
                        )
                        
                        browser.close()
                        
                        return {
                            "x-s": encrypt_params["X-s"],
                            "x-t": str(encrypt_params["X-t"])
                        }
                        
                except Exception as e:
                    self.logger.warning(f"签名失败，重试中: {e}")
                    continue
            
            raise Exception("签名失败，已重试多次")

        except Exception as e:
            self.logger.error(f"签名函数执行失败: {e}")
            raise

    async def upload_content(self,
                           file_paths: List[str],
                           title: str,
                           description: str = "",
                           tags: List[str] = None,
                           **kwargs) -> UploadResult:
        """上传内容到小红书"""
        try:
            # 验证文件
            is_valid, error_msg = self.validate_files(file_paths)
            if not is_valid:
                return UploadResult(success=False, error=error_msg)

            # 确定内容类型
            main_file = file_paths[0]
            is_video = main_file.lower().endswith(('.mp4', '.mov', '.avi'))

            # 获取封面图片
            cover_path = None
            if len(file_paths) > 1:
                # 如果有多个文件，第二个可能是封面
                for file_path in file_paths[1:]:
                    if file_path.lower().endswith(('.jpg', '.jpeg', '.png')):
                        cover_path = file_path
                        break

            # 设置隐私状态
            is_private = kwargs.get('is_private', True)  # 默认私密发布

            # 上传内容
            if is_video:
                # 上传视频
                result = self.xhs_client.create_video_note(
                    title=title,
                    video_path=main_file,
                    desc=description,
                    cover_path=cover_path,
                    is_private=is_private
                )
            else:
                # 上传图片
                image_paths = [f for f in file_paths if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                result = self.xhs_client.create_image_note(
                    title=title,
                    image_paths=image_paths,
                    desc=description,
                    is_private=is_private
                )

            if result and result.get('success'):
                self.logger.info(f"上传成功: {title}")
                return UploadResult(
                    success=True,
                    content_id=result.get('note_id', ''),
                    url=result.get('note_url', ''),
                    message="上传成功",
                    extra_data=result
                )
            else:
                error_msg = result.get('msg', '上传失败') if result else '上传失败'
                self.logger.error(f"上传失败: {error_msg}")
                return UploadResult(success=False, error=error_msg)

        except Exception as e:
            self.logger.error(f"上传内容失败: {e}")
            return UploadResult(success=False, error=str(e))

    def validate_account(self) -> bool:
        """验证账号有效性"""
        if not super().validate_account():
            return False

        try:
            # 尝试获取用户信息来验证账号
            user_info = self.xhs_client.get_self_info()
            if user_info:
                self.logger.info(f"账号验证成功: {user_info.get('nickname', 'unknown')}")
                return True
            else:
                self.logger.error("无法获取用户信息，账号可能无效")
                return False
        except Exception as e:
            self.logger.error(f"账号验证失败: {e}")
            return False
