"""
账号数据模型
"""

from enum import Enum
from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict
import json


class PlatformType(Enum):
    """平台类型枚举"""
    TIKTOK = "tiktok"
    DOUYIN = "douyin"
    XIAOHONGSHU = "xiaohongshu"
    BILIBILI = "bilibili"
    YOUTUBE = "youtube"


class AccountType(Enum):
    """账号类型枚举"""
    MONITOR = "monitor"    # 监控账号
    UPLOAD = "upload"      # 上传账号
    BOTH = "both"         # 既可监控又可上传


@dataclass
class Account:
    """账号模型"""
    
    # 基本信息
    id: Optional[int] = None
    name: str = ""
    platform: PlatformType = PlatformType.TIKTOK
    account_type: AccountType = AccountType.BOTH
    username: str = ""
    user_id: str = ""
    
    # 认证信息 (加密存储)
    cookies: str = ""
    access_token: str = ""
    refresh_token: str = ""
    
    # 配置信息
    config: Dict[str, Any] = None
    
    # 状态信息
    is_active: bool = True
    last_used: Optional[datetime] = None
    
    # 时间戳
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.config is None:
            self.config = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理枚举类型
        data['platform'] = self.platform.value
        data['account_type'] = self.account_type.value
        # 处理日期时间
        for key in ['last_used', 'created_at', 'updated_at']:
            if data[key]:
                data[key] = data[key].isoformat()
        # 处理JSON字段
        data['config'] = json.dumps(self.config) if self.config else '{}'
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Account':
        """从字典创建账号"""
        # 处理枚举类型
        if 'platform' in data:
            data['platform'] = PlatformType(data['platform'])
        if 'account_type' in data:
            data['account_type'] = AccountType(data['account_type'])
        
        # 处理日期时间
        for key in ['last_used', 'created_at', 'updated_at']:
            if data.get(key):
                data[key] = datetime.fromisoformat(data[key])
        
        # 处理JSON字段
        if 'config' in data and isinstance(data['config'], str):
            data['config'] = json.loads(data['config']) if data['config'] else {}
        
        return cls(**data)
    
    def update_last_used(self):
        """更新最后使用时间"""
        self.last_used = datetime.now()
        self.updated_at = datetime.now()
    
    def can_monitor(self) -> bool:
        """检查是否可以用于监控"""
        return self.account_type in [AccountType.MONITOR, AccountType.BOTH] and self.is_active
    
    def can_upload(self) -> bool:
        """检查是否可以用于上传"""
        return self.account_type in [AccountType.UPLOAD, AccountType.BOTH] and self.is_active
    
    def get_platform_name(self) -> str:
        """获取平台中文名称"""
        platform_names = {
            PlatformType.TIKTOK: "TikTok",
            PlatformType.DOUYIN: "抖音",
            PlatformType.XIAOHONGSHU: "小红书",
            PlatformType.BILIBILI: "哔哩哔哩",
            PlatformType.YOUTUBE: "YouTube"
        }
        return platform_names.get(self.platform, self.platform.value)
    
    def __str__(self) -> str:
        return f"Account(id={self.id}, name='{self.name}', platform={self.platform.value})"
    
    def __repr__(self) -> str:
        return self.__str__()
