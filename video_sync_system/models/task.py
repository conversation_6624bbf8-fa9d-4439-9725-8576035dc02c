"""
任务数据模型
"""

from enum import Enum
from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict
import json


class TaskStatus(Enum):
    """任务状态枚举"""
    CREATED = "created"          # 已创建
    RUNNING = "running"          # 运行中
    PAUSED = "paused"           # 已暂停
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"      # 已取消


@dataclass
class Task:
    """任务模型"""
    
    # 基本信息
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    
    # 平台配置
    source_platform: str = ""      # 监控平台 (tiktok, douyin, xiaohongshu, bilibili)
    target_platform: str = ""      # 上传平台 (tiktok, youtube, xiaohongshu, bilibili)
    source_account_id: int = 0     # 监控账号ID
    target_account_id: int = 0     # 上传账号ID
    
    # 任务配置
    check_interval: int = 3600     # 检查间隔(秒)
    max_downloads: int = 0         # 最大下载数量(0=无限制)
    auto_upload: bool = True       # 是否自动上传
    
    # 状态信息
    status: TaskStatus = TaskStatus.CREATED
    last_check_time: Optional[datetime] = None
    last_content_time: Optional[datetime] = None
    total_downloaded: int = 0
    total_uploaded: int = 0
    
    # 时间戳
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # 扩展配置
    config: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.config is None:
            self.config = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理枚举类型
        data['status'] = self.status.value
        # 处理日期时间
        for key in ['last_check_time', 'last_content_time', 'created_at', 'updated_at']:
            if data[key]:
                data[key] = data[key].isoformat()
        # 处理JSON字段
        data['config'] = json.dumps(self.config) if self.config else '{}'
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """从字典创建任务"""
        # 处理枚举类型
        if 'status' in data:
            data['status'] = TaskStatus(data['status'])
        
        # 处理日期时间
        for key in ['last_check_time', 'last_content_time', 'created_at', 'updated_at']:
            if data.get(key):
                data[key] = datetime.fromisoformat(data[key])
        
        # 处理JSON字段
        if 'config' in data and isinstance(data['config'], str):
            data['config'] = json.loads(data['config']) if data['config'] else {}
        
        return cls(**data)
    
    def update_status(self, status: TaskStatus):
        """更新任务状态"""
        self.status = status
        self.updated_at = datetime.now()
    
    def update_check_time(self):
        """更新检查时间"""
        self.last_check_time = datetime.now()
        self.updated_at = datetime.now()
    
    def increment_downloaded(self):
        """增加下载计数"""
        self.total_downloaded += 1
        self.updated_at = datetime.now()
    
    def increment_uploaded(self):
        """增加上传计数"""
        self.total_uploaded += 1
        self.updated_at = datetime.now()
    
    def is_active(self) -> bool:
        """检查任务是否处于活跃状态"""
        return self.status in [TaskStatus.CREATED, TaskStatus.RUNNING]
    
    def can_run(self) -> bool:
        """检查任务是否可以运行"""
        return self.status in [TaskStatus.CREATED, TaskStatus.PAUSED]
    
    def __str__(self) -> str:
        return f"Task(id={self.id}, name='{self.name}', status={self.status.value})"
    
    def __repr__(self) -> str:
        return self.__str__()
