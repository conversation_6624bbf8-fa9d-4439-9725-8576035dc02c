#!/usr/bin/env python3
"""
视频同步系统主程序
"""

import os
import sys
import asyncio
import argparse
import signal
from typing import Optional

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.task_manager import TaskManager
from core.scheduler import TaskScheduler
from core.database import Database
from models.task import Task, TaskStatus
from models.account import Account, PlatformType, AccountType
from utils.logger import setup_logger, get_logger
from utils.config import config

logger = get_logger(__name__)


class VideoSyncApp:
    """视频同步应用主类"""
    
    def __init__(self):
        self.task_manager = None
        self.scheduler = None
        self.db = None
        self.running = False
    
    async def initialize(self):
        """初始化应用"""
        try:
            # 设置日志
            log_config = config.get_log_config()
            setup_logger(**log_config)
            
            # 确保必要目录存在
            config.ensure_directories()
            
            # 初始化数据库
            self.db = Database()
            self.db.init_database()
            
            # 初始化任务管理器
            self.task_manager = TaskManager()
            
            # 初始化调度器
            self.scheduler = TaskScheduler(self.task_manager)
            
            logger.info("应用初始化完成")
            
        except Exception as e:
            logger.error(f"应用初始化失败: {e}")
            raise
    
    async def start_daemon(self):
        """启动守护进程模式"""
        try:
            await self.initialize()
            
            # 启动调度器
            await self.scheduler.start()
            self.running = True
            
            logger.info("视频同步系统已启动 (守护进程模式)")
            
            # 设置信号处理
            def signal_handler(signum, frame):
                logger.info(f"收到信号 {signum}，准备退出...")
                asyncio.create_task(self.stop())
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            # 保持运行
            while self.running:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"启动守护进程失败: {e}")
            raise
    
    async def stop(self):
        """停止应用"""
        try:
            logger.info("正在停止视频同步系统...")
            self.running = False
            
            if self.scheduler:
                await self.scheduler.stop()
            
            if self.task_manager:
                self.task_manager.cleanup()
            
            if self.db:
                self.db.close()
            
            logger.info("视频同步系统已停止")
            
        except Exception as e:
            logger.error(f"停止应用失败: {e}")
    
    async def create_account(self, 
                           name: str,
                           platform: str,
                           account_type: str,
                           cookies: str,
                           **kwargs) -> Optional[str]:
        """创建账号"""
        try:
            if not self.db:
                await self.initialize()
            
            # 转换枚举类型
            platform_enum = PlatformType(platform.upper())
            account_type_enum = AccountType(account_type.upper())
            
            # 创建账号对象
            account = Account(
                name=name,
                platform=platform_enum,
                account_type=account_type_enum,
                cookies=cookies,
                config=kwargs
            )
            
            # 保存到数据库
            account_id = self.db.create_account(account)
            if account_id:
                logger.info(f"账号创建成功: {name} (ID: {account_id})")
                return str(account_id)
            else:
                logger.error("账号创建失败")
                return None
                
        except Exception as e:
            logger.error(f"创建账号失败: {e}")
            return None
    
    async def create_task(self,
                         name: str,
                         source_platform: str,
                         target_platforms: list,
                         source_user_id: str,
                         monitor_account_id: str,
                         upload_account_ids: dict,
                         check_interval: int = 3600,
                         download_limit: int = 10) -> Optional[str]:
        """创建任务"""
        try:
            if not self.task_manager:
                await self.initialize()
            
            # 转换平台类型
            source_platform_enum = PlatformType(source_platform.upper())
            target_platforms_enum = [PlatformType(p.upper()) for p in target_platforms]
            
            # 转换上传账号ID映射
            upload_account_ids_enum = {}
            for platform, account_id in upload_account_ids.items():
                upload_account_ids_enum[PlatformType(platform.upper())] = account_id
            
            # 创建任务
            task = await self.task_manager.create_task(
                name=name,
                source_platform=source_platform_enum,
                target_platforms=target_platforms_enum,
                source_user_id=source_user_id,
                monitor_account_id=monitor_account_id,
                upload_account_ids=upload_account_ids_enum,
                check_interval=check_interval,
                download_limit=download_limit
            )
            
            if task:
                logger.info(f"任务创建成功: {name} (ID: {task.id})")
                return task.id
            else:
                logger.error("任务创建失败")
                return None
                
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return None
    
    async def start_task(self, task_id: str) -> bool:
        """启动任务"""
        try:
            if not self.task_manager:
                await self.initialize()
            
            success = await self.task_manager.start_task(task_id)
            
            # 如果调度器已启动，添加到调度器
            if success and self.scheduler and self.scheduler.running:
                task = self.db.get_task(task_id)
                if task:
                    await self.scheduler.add_task(task)
            
            return success
            
        except Exception as e:
            logger.error(f"启动任务失败: {e}")
            return False
    
    async def stop_task(self, task_id: str) -> bool:
        """停止任务"""
        try:
            if not self.task_manager:
                await self.initialize()
            
            success = await self.task_manager.stop_task(task_id)
            
            # 从调度器移除
            if success and self.scheduler:
                await self.scheduler.remove_task(task_id)
            
            return success
            
        except Exception as e:
            logger.error(f"停止任务失败: {e}")
            return False
    
    async def list_tasks(self) -> list:
        """列出所有任务"""
        try:
            if not self.task_manager:
                await self.initialize()
            
            return await self.task_manager.list_tasks()
            
        except Exception as e:
            logger.error(f"列出任务失败: {e}")
            return []
    
    async def list_accounts(self) -> list:
        """列出所有账号"""
        try:
            if not self.db:
                await self.initialize()
            
            accounts = self.db.get_all_accounts()
            account_list = []
            
            for account in accounts:
                account_info = {
                    "id": account.id,
                    "name": account.name,
                    "platform": account.platform.value,
                    "account_type": account.account_type.value,
                    "is_active": account.is_active,
                    "created_at": account.created_at.isoformat() if account.created_at else None
                }
                account_list.append(account_info)
            
            return account_list
            
        except Exception as e:
            logger.error(f"列出账号失败: {e}")
            return []


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='视频同步系统')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 守护进程命令
    daemon_parser = subparsers.add_parser('daemon', help='启动守护进程')
    
    # 账号管理命令
    account_parser = subparsers.add_parser('account', help='账号管理')
    account_subparsers = account_parser.add_subparsers(dest='account_action')
    
    # 创建账号
    create_account_parser = account_subparsers.add_parser('create', help='创建账号')
    create_account_parser.add_argument('--name', required=True, help='账号名称')
    create_account_parser.add_argument('--platform', required=True, 
                                     choices=['xiaohongshu', 'tiktok', 'douyin', 'bilibili', 'youtube'],
                                     help='平台类型')
    create_account_parser.add_argument('--type', required=True,
                                     choices=['monitor', 'upload', 'both'],
                                     help='账号类型')
    create_account_parser.add_argument('--cookies', required=True, help='Cookie字符串')
    
    # 列出账号
    account_subparsers.add_parser('list', help='列出所有账号')
    
    # 任务管理命令
    task_parser = subparsers.add_parser('task', help='任务管理')
    task_subparsers = task_parser.add_subparsers(dest='task_action')
    
    # 创建任务
    create_task_parser = task_subparsers.add_parser('create', help='创建任务')
    create_task_parser.add_argument('--name', required=True, help='任务名称')
    create_task_parser.add_argument('--source-platform', required=True, help='源平台')
    create_task_parser.add_argument('--target-platforms', required=True, nargs='+', help='目标平台列表')
    create_task_parser.add_argument('--source-user-id', required=True, help='源用户ID')
    create_task_parser.add_argument('--monitor-account-id', required=True, help='监控账号ID')
    create_task_parser.add_argument('--upload-account-ids', required=True, help='上传账号ID映射(JSON格式)')
    create_task_parser.add_argument('--check-interval', type=int, default=3600, help='检查间隔(秒)')
    create_task_parser.add_argument('--download-limit', type=int, default=10, help='下载限制')
    
    # 启动任务
    start_task_parser = task_subparsers.add_parser('start', help='启动任务')
    start_task_parser.add_argument('task_id', help='任务ID')
    
    # 停止任务
    stop_task_parser = task_subparsers.add_parser('stop', help='停止任务')
    stop_task_parser.add_argument('task_id', help='任务ID')
    
    # 列出任务
    task_subparsers.add_parser('list', help='列出所有任务')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    app = VideoSyncApp()
    
    try:
        if args.command == 'daemon':
            await app.start_daemon()
        
        elif args.command == 'account':
            if args.account_action == 'create':
                account_id = await app.create_account(
                    name=args.name,
                    platform=args.platform,
                    account_type=args.type,
                    cookies=args.cookies
                )
                if account_id:
                    print(f"账号创建成功，ID: {account_id}")
                else:
                    print("账号创建失败")
                    sys.exit(1)
            
            elif args.account_action == 'list':
                accounts = await app.list_accounts()
                if accounts:
                    print("账号列表:")
                    for account in accounts:
                        print(f"  ID: {account['id']}, 名称: {account['name']}, "
                              f"平台: {account['platform']}, 类型: {account['account_type']}")
                else:
                    print("没有找到账号")
        
        elif args.command == 'task':
            if args.task_action == 'create':
                import json
                upload_account_ids = json.loads(args.upload_account_ids)
                
                task_id = await app.create_task(
                    name=args.name,
                    source_platform=args.source_platform,
                    target_platforms=args.target_platforms,
                    source_user_id=args.source_user_id,
                    monitor_account_id=args.monitor_account_id,
                    upload_account_ids=upload_account_ids,
                    check_interval=args.check_interval,
                    download_limit=args.download_limit
                )
                
                if task_id:
                    print(f"任务创建成功，ID: {task_id}")
                else:
                    print("任务创建失败")
                    sys.exit(1)
            
            elif args.task_action == 'start':
                success = await app.start_task(args.task_id)
                if success:
                    print(f"任务启动成功: {args.task_id}")
                else:
                    print(f"任务启动失败: {args.task_id}")
                    sys.exit(1)
            
            elif args.task_action == 'stop':
                success = await app.stop_task(args.task_id)
                if success:
                    print(f"任务停止成功: {args.task_id}")
                else:
                    print(f"任务停止失败: {args.task_id}")
                    sys.exit(1)
            
            elif args.task_action == 'list':
                tasks = await app.list_tasks()
                if tasks:
                    print("任务列表:")
                    for task in tasks:
                        print(f"  ID: {task['id']}, 名称: {task['name']}, "
                              f"状态: {task['status']}, 运行中: {task['is_running']}")
                else:
                    print("没有找到任务")
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)
    finally:
        await app.stop()


if __name__ == '__main__':
    asyncio.run(main())
