#!/usr/bin/env python3
"""
视频同步系统 Web 界面演示脚本
用于创建示例数据和测试Web界面功能
"""

import sys
import sqlite3
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database import Database


def create_demo_accounts():
    """创建演示账号数据"""
    print("创建演示账号数据...")
    
    db_manager = Database()
    
    # 示例账号数据
    demo_accounts = [
        {
            'name': '小红书监控账号1',
            'platform': 'xiaohongshu',
            'account_type': 'monitor',
            'cookies': 'demo_cookies_xhs_monitor_1',
            'config': json.dumps({'proxy': None, 'user_agent': 'Mozilla/5.0...'}),
            'is_active': True
        },
        {
            'name': '抖音监控账号1',
            'platform': 'douyin',
            'account_type': 'monitor',
            'cookies': 'demo_cookies_douyin_monitor_1',
            'config': json.dumps({'proxy': None}),
            'is_active': True
        },
        {
            'name': 'TikTok上传账号1',
            'platform': 'tiktok',
            'account_type': 'upload',
            'cookies': 'demo_cookies_tiktok_upload_1',
            'config': json.dumps({'upload_settings': {'quality': 'HD'}}),
            'is_active': True
        },
        {
            'name': 'YouTube上传账号1',
            'platform': 'youtube',
            'account_type': 'upload',
            'cookies': 'demo_cookies_youtube_upload_1',
            'config': json.dumps({'api_key': 'demo_api_key'}),
            'is_active': False
        },
        {
            'name': 'B站上传账号1',
            'platform': 'bilibili',
            'account_type': 'upload',
            'cookies': 'demo_cookies_bilibili_upload_1',
            'config': json.dumps({'upload_line': 'auto'}),
            'is_active': True
        }
    ]
    
    conn = sqlite3.connect(db_manager.db_path)
    cursor = conn.cursor()
    
    for account in demo_accounts:
        cursor.execute('''
            INSERT OR REPLACE INTO accounts
            (name, platform, account_type, cookies, config, is_active, created_at, updated_at, last_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            account['name'],
            account['platform'],
            account['account_type'],
            account['cookies'],
            account['config'],
            account['is_active'],
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            datetime.now().isoformat() if account['is_active'] else None
        ))
    
    conn.commit()
    conn.close()
    print(f"已创建 {len(demo_accounts)} 个演示账号")


def create_demo_tasks():
    """创建演示任务数据"""
    print("创建演示任务数据...")
    
    db_manager = Database()

    # 示例任务数据
    demo_tasks = [
        {
            'name': '小红书美食博主同步',
            'source_platform': 'xiaohongshu',
            'source_user_id': 'foodie_blogger_123',
            'target_platforms': json.dumps(['tiktok', 'bilibili']),
            'monitor_account_id': 1,
            'upload_accounts': json.dumps({'tiktok': 3, 'bilibili': 5}),
            'check_interval': 3600,
            'download_limit': 5,
            'status': 'running',
            'success_count': 12,
            'error_count': 1
        },
        {
            'name': '抖音舞蹈达人同步',
            'source_platform': 'douyin',
            'source_user_id': 'dance_master_456',
            'target_platforms': json.dumps(['tiktok', 'youtube']),
            'monitor_account_id': 2,
            'upload_accounts': json.dumps({'tiktok': 3, 'youtube': 4}),
            'check_interval': 1800,
            'download_limit': 3,
            'status': 'paused',
            'success_count': 8,
            'error_count': 0
        },
        {
            'name': 'TikTok搞笑视频同步',
            'source_platform': 'tiktok',
            'source_user_id': 'funny_videos_789',
            'target_platforms': json.dumps(['douyin', 'bilibili']),
            'monitor_account_id': 3,
            'upload_accounts': json.dumps({'douyin': 2, 'bilibili': 5}),
            'check_interval': 7200,
            'download_limit': 10,
            'status': 'failed',
            'success_count': 5,
            'error_count': 3
        }
    ]
    
    conn = sqlite3.connect(db_manager.db_path)
    cursor = conn.cursor()
    
    for i, task in enumerate(demo_tasks):
        created_at = datetime.now() - timedelta(days=i+1)
        last_run = datetime.now() - timedelta(hours=i*2) if task['status'] != 'failed' else None
        next_run = datetime.now() + timedelta(seconds=task['check_interval']) if task['status'] == 'running' else None
        
        cursor.execute('''
            INSERT OR REPLACE INTO tasks
            (name, source_platform, source_user_id, target_platforms,
             monitor_account_id, upload_accounts, check_interval, download_limit,
             status, success_count, error_count, created_at, updated_at, last_run, next_run)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            task['name'],
            task['source_platform'],
            task['source_user_id'],
            task['target_platforms'],
            task['monitor_account_id'],
            task['upload_accounts'],
            task['check_interval'],
            task['download_limit'],
            task['status'],
            task['success_count'],
            task['error_count'],
            created_at.isoformat(),
            created_at.isoformat(),
            last_run.isoformat() if last_run else None,
            next_run.isoformat() if next_run else None
        ))
    
    conn.commit()
    conn.close()
    print(f"已创建 {len(demo_tasks)} 个演示任务")


def create_demo_logs():
    """创建演示日志数据"""
    print("创建演示日志数据...")
    
    db_manager = Database()

    # 示例日志数据
    demo_logs = [
        {
            'level': 'INFO',
            'source': 'task_manager',
            'message': '任务调度器启动成功',
            'timestamp': datetime.now() - timedelta(minutes=30)
        },
        {
            'level': 'INFO',
            'source': 'scheduler',
            'message': '开始执行任务: 小红书美食博主同步',
            'timestamp': datetime.now() - timedelta(minutes=25)
        },
        {
            'level': 'INFO',
            'source': 'platform',
            'message': '成功获取用户 foodie_blogger_123 的最新视频列表',
            'timestamp': datetime.now() - timedelta(minutes=20)
        },
        {
            'level': 'WARNING',
            'source': 'platform',
            'message': '视频下载速度较慢，可能受到网络限制',
            'timestamp': datetime.now() - timedelta(minutes=15)
        },
        {
            'level': 'ERROR',
            'source': 'platform',
            'message': '上传到YouTube失败: API配额已用完',
            'timestamp': datetime.now() - timedelta(minutes=10)
        },
        {
            'level': 'INFO',
            'source': 'web_server',
            'message': 'Web服务器启动，监听端口 5000',
            'timestamp': datetime.now() - timedelta(minutes=5)
        }
    ]
    
    conn = sqlite3.connect(db_manager.db_path)
    cursor = conn.cursor()
    
    # 创建日志表（如果不存在）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT NOT NULL,
            level TEXT NOT NULL,
            source TEXT NOT NULL,
            message TEXT NOT NULL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    for log in demo_logs:
        cursor.execute('''
            INSERT INTO logs (timestamp, level, source, message)
            VALUES (?, ?, ?, ?)
        ''', (
            log['timestamp'].isoformat(),
            log['level'],
            log['source'],
            log['message']
        ))
    
    conn.commit()
    conn.close()
    print(f"已创建 {len(demo_logs)} 条演示日志")


def main():
    """主函数"""
    print("=" * 60)
    print("视频同步系统 Web 界面演示数据创建")
    print("=" * 60)
    
    # 确保数据库已初始化
    db_manager = Database()
    
    # 创建演示数据
    create_demo_accounts()
    create_demo_tasks()
    create_demo_logs()
    
    print("\n" + "=" * 60)
    print("演示数据创建完成！")
    print("现在可以启动Web服务器查看演示效果：")
    print("python run_web.py --debug")
    print("然后访问: http://localhost:5000")
    print("=" * 60)


if __name__ == '__main__':
    main()
