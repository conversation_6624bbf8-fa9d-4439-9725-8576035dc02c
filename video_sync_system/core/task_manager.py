"""
任务管理器
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor

from models.task import Task, TaskStatus
from models.account import Account, PlatformType
from platforms import BaseMonitor, BaseUploader, ContentItem, UploadResult
from platforms.xiaohongshu import <PERSON>HongShuMonitor, XiaoHongShuUploader
from .database import Database
from utils.logger import get_logger
from utils.config import config
from utils.file_utils import FileUtils

logger = get_logger(__name__)


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.db = Database()
        self.running_tasks = {}  # 正在运行的任务
        self.executor = ThreadPoolExecutor(max_workers=config.get("MAX_CONCURRENT_TASKS", 5, int))
        
        # 平台适配器映射
        self.monitor_adapters = {
            PlatformType.XIAOHONGSHU: XiaoHongShuMonitor,
        }
        
        self.uploader_adapters = {
            PlatformType.XIAOHONGSHU: <PERSON><PERSON>ongShuUploader,
        }
    
    async def create_task(self, 
                         name: str,
                         source_platform: PlatformType,
                         target_platforms: List[PlatformType],
                         source_user_id: str,
                         monitor_account_id: str,
                         upload_account_ids: Dict[PlatformType, str],
                         check_interval: int = 3600,
                         download_limit: int = 10,
                         **kwargs) -> Optional[Task]:
        """创建新任务"""
        try:
            # 验证账号
            monitor_account = self.db.get_account(monitor_account_id)
            if not monitor_account or monitor_account.platform != source_platform:
                logger.error(f"监控账号无效: {monitor_account_id}")
                return None
            
            upload_accounts = {}
            for platform, account_id in upload_account_ids.items():
                account = self.db.get_account(account_id)
                if not account or account.platform != platform:
                    logger.error(f"上传账号无效: {account_id}")
                    return None
                upload_accounts[platform] = account
            
            # 创建任务
            task = Task(
                name=name,
                source_platform=source_platform,
                target_platforms=target_platforms,
                source_user_id=source_user_id,
                monitor_account_id=monitor_account_id,
                upload_account_ids=upload_account_ids,
                check_interval=check_interval,
                download_limit=download_limit,
                config=kwargs
            )
            
            # 保存到数据库
            task_id = self.db.create_task(task)
            if task_id:
                task.id = task_id
                logger.info(f"任务创建成功: {task.name} (ID: {task_id})")
                return task
            else:
                logger.error("任务保存失败")
                return None
                
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return None
    
    async def start_task(self, task_id: str) -> bool:
        """启动任务"""
        try:
            task = self.db.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            if task.status == TaskStatus.RUNNING:
                logger.warning(f"任务已在运行: {task.name}")
                return True
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.last_run = datetime.now()
            self.db.update_task(task)
            
            # 启动任务执行
            self.running_tasks[task_id] = asyncio.create_task(self._execute_task(task))
            
            logger.info(f"任务启动成功: {task.name}")
            return True
            
        except Exception as e:
            logger.error(f"启动任务失败: {e}")
            return False
    
    async def stop_task(self, task_id: str) -> bool:
        """停止任务"""
        try:
            task = self.db.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            # 取消运行中的任务
            if task_id in self.running_tasks:
                self.running_tasks[task_id].cancel()
                del self.running_tasks[task_id]
            
            # 更新任务状态
            task.status = TaskStatus.PAUSED
            self.db.update_task(task)
            
            logger.info(f"任务停止成功: {task.name}")
            return True
            
        except Exception as e:
            logger.error(f"停止任务失败: {e}")
            return False
    
    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        try:
            # 先停止任务
            await self.stop_task(task_id)
            
            # 从数据库删除
            success = self.db.delete_task(task_id)
            if success:
                logger.info(f"任务删除成功: {task_id}")
            else:
                logger.error(f"任务删除失败: {task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            return False
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            task = self.db.get_task(task_id)
            if not task:
                return None
            
            return {
                "id": task.id,
                "name": task.name,
                "status": task.status.value,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "last_run": task.last_run.isoformat() if task.last_run else None,
                "next_run": task.next_run.isoformat() if task.next_run else None,
                "success_count": task.success_count,
                "error_count": task.error_count,
                "last_error": task.last_error,
                "is_running": task_id in self.running_tasks
            }
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return None
    
    async def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任务"""
        try:
            tasks = self.db.get_all_tasks()
            task_list = []
            
            for task in tasks:
                task_info = {
                    "id": task.id,
                    "name": task.name,
                    "status": task.status.value,
                    "source_platform": task.source_platform.value,
                    "target_platforms": [p.value for p in task.target_platforms],
                    "source_user_id": task.source_user_id,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "last_run": task.last_run.isoformat() if task.last_run else None,
                    "success_count": task.success_count,
                    "error_count": task.error_count,
                    "is_running": task.id in self.running_tasks
                }
                task_list.append(task_info)
            
            return task_list
            
        except Exception as e:
            logger.error(f"列出任务失败: {e}")
            return []
    
    async def _execute_task(self, task: Task):
        """执行任务"""
        logger.info(f"开始执行任务: {task.name}")
        
        try:
            while task.status == TaskStatus.RUNNING:
                try:
                    # 执行一次任务
                    await self._run_task_once(task)
                    
                    # 更新成功计数和下次运行时间
                    task.success_count += 1
                    task.last_run = datetime.now()
                    task.next_run = datetime.now() + timedelta(seconds=task.check_interval)
                    task.last_error = None
                    
                    self.db.update_task(task)
                    
                    # 等待下次执行
                    await asyncio.sleep(task.check_interval)
                    
                except asyncio.CancelledError:
                    logger.info(f"任务被取消: {task.name}")
                    break
                    
                except Exception as e:
                    logger.error(f"任务执行出错: {task.name}, 错误: {e}")
                    
                    # 更新错误信息
                    task.error_count += 1
                    task.last_error = str(e)
                    task.last_run = datetime.now()
                    
                    # 如果错误次数过多，暂停任务
                    if task.error_count >= 5:
                        task.status = TaskStatus.FAILED
                        logger.error(f"任务失败次数过多，已暂停: {task.name}")
                    
                    self.db.update_task(task)
                    
                    # 等待一段时间后重试
                    await asyncio.sleep(min(task.check_interval, 300))  # 最多等待5分钟
                    
        except Exception as e:
            logger.error(f"任务执行异常: {task.name}, 错误: {e}")
            task.status = TaskStatus.FAILED
            task.last_error = str(e)
            self.db.update_task(task)
        
        finally:
            # 清理运行中的任务记录
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            
            logger.info(f"任务执行结束: {task.name}")
    
    async def _run_task_once(self, task: Task):
        """执行一次任务"""
        logger.debug(f"执行任务: {task.name}")
        
        # 获取监控账号
        monitor_account = self.db.get_account(task.monitor_account_id)
        if not monitor_account:
            raise Exception(f"监控账号不存在: {task.monitor_account_id}")
        
        # 创建监控适配器
        monitor_class = self.monitor_adapters.get(task.source_platform)
        if not monitor_class:
            raise Exception(f"不支持的源平台: {task.source_platform}")
        
        monitor = monitor_class(monitor_account)
        
        # 获取最新内容
        since_time = task.last_run if task.last_run else datetime.now() - timedelta(hours=24)
        content_items = await monitor.get_latest_content(
            task.source_user_id, 
            task.download_limit,
            since_time
        )
        
        if not content_items:
            logger.debug(f"没有新内容: {task.name}")
            return
        
        logger.info(f"发现 {len(content_items)} 条新内容: {task.name}")
        
        # 处理每个内容项
        for content_item in content_items:
            try:
                await self._process_content_item(task, monitor, content_item)
            except Exception as e:
                logger.error(f"处理内容失败: {content_item.id}, 错误: {e}")
                continue

    async def _process_content_item(self, task: Task, monitor: BaseMonitor, content_item: ContentItem):
        """处理单个内容项"""
        logger.info(f"处理内容: {content_item.title} ({content_item.id})")

        # 检查是否已经处理过
        if self.db.content_exists(content_item.id, task.id):
            logger.debug(f"内容已处理过: {content_item.id}")
            return

        # 下载内容
        download_dir = config.get("DOWNLOAD_PATH", "data/downloads")
        task_download_dir = f"{download_dir}/{task.id}/{content_item.id}"

        try:
            downloaded_files = await monitor.download_content(content_item, task_download_dir)
            if not downloaded_files:
                logger.warning(f"下载失败: {content_item.id}")
                return

            logger.info(f"下载完成: {len(downloaded_files)} 个文件")

            # 记录内容
            self.db.create_content_record(
                content_id=content_item.id,
                task_id=task.id,
                title=content_item.title,
                source_url=content_item.url,
                local_paths=downloaded_files,
                metadata=content_item.__dict__
            )

            # 上传到目标平台
            for target_platform in task.target_platforms:
                try:
                    await self._upload_to_platform(task, content_item, downloaded_files, target_platform)
                except Exception as e:
                    logger.error(f"上传到 {target_platform.value} 失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"处理内容项失败: {content_item.id}, 错误: {e}")
            raise

    async def _upload_to_platform(self,
                                task: Task,
                                content_item: ContentItem,
                                file_paths: List[str],
                                target_platform: PlatformType):
        """上传到目标平台"""
        logger.info(f"上传到 {target_platform.value}: {content_item.title}")

        # 获取上传账号
        upload_account_id = task.upload_account_ids.get(target_platform)
        if not upload_account_id:
            logger.warning(f"未配置 {target_platform.value} 上传账号")
            return

        upload_account = self.db.get_account(upload_account_id)
        if not upload_account:
            logger.error(f"上传账号不存在: {upload_account_id}")
            return

        # 创建上传适配器
        uploader_class = self.uploader_adapters.get(target_platform)
        if not uploader_class:
            logger.error(f"不支持的目标平台: {target_platform}")
            return

        uploader = uploader_class(upload_account)

        # 验证账号
        if not uploader.validate_account():
            logger.error(f"上传账号验证失败: {upload_account.name}")
            return

        try:
            # 准备上传内容
            upload_data = await uploader.prepare_content(content_item, file_paths)

            # 执行上传
            result = await uploader.upload_content(
                file_paths=file_paths,
                title=upload_data.get("title", content_item.title),
                description=upload_data.get("description", content_item.description),
                tags=upload_data.get("tags", content_item.tags),
                **task.config  # 传递任务配置
            )

            if result.success:
                logger.info(f"上传成功到 {target_platform.value}: {result.url}")

                # 更新内容记录
                self.db.update_content_record_upload_status(
                    content_item.id,
                    task.id,
                    target_platform,
                    True,
                    result.url,
                    result.content_id
                )
            else:
                logger.error(f"上传失败到 {target_platform.value}: {result.error}")

                # 记录失败状态
                self.db.update_content_record_upload_status(
                    content_item.id,
                    task.id,
                    target_platform,
                    False,
                    "",
                    "",
                    result.error
                )

        except Exception as e:
            logger.error(f"上传异常到 {target_platform.value}: {e}")

            # 记录异常状态
            self.db.update_content_record_upload_status(
                content_item.id,
                task.id,
                target_platform,
                False,
                "",
                "",
                str(e)
            )

    def cleanup(self):
        """清理资源"""
        logger.info("清理任务管理器资源")

        # 取消所有运行中的任务
        for task_id, task_future in self.running_tasks.items():
            task_future.cancel()

        # 关闭线程池
        self.executor.shutdown(wait=True)

        # 关闭数据库连接
        self.db.close()

        logger.info("任务管理器清理完成")
