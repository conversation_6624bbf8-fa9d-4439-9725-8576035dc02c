"""
数据库操作类
"""

import sqlite3
import os
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
from datetime import datetime

from models.task import Task, TaskStatus
from models.account import Account, PlatformType, AccountType
from utils.logger import get_logger

logger = get_logger(__name__)


class Database:
    """数据库操作类"""
    
    def __init__(self, db_path: str = "data/database.db"):
        self.db_path = db_path
        self._ensure_db_dir()
        self._init_tables()
    
    def _ensure_db_dir(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def _init_tables(self):
        """初始化数据库表"""
        with self.get_connection() as conn:
            # 创建任务表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    source_platform TEXT NOT NULL,
                    target_platform TEXT NOT NULL,
                    source_account_id INTEGER NOT NULL,
                    target_account_id INTEGER NOT NULL,
                    check_interval INTEGER DEFAULT 3600,
                    max_downloads INTEGER DEFAULT 0,
                    auto_upload BOOLEAN DEFAULT 1,
                    status TEXT DEFAULT 'created',
                    last_check_time TEXT,
                    last_content_time TEXT,
                    total_downloaded INTEGER DEFAULT 0,
                    total_uploaded INTEGER DEFAULT 0,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    config TEXT DEFAULT '{}'
                )
            ''')
            
            # 创建账号表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    platform TEXT NOT NULL,
                    account_type TEXT NOT NULL,
                    username TEXT,
                    user_id TEXT,
                    cookies TEXT,
                    access_token TEXT,
                    refresh_token TEXT,
                    config TEXT DEFAULT '{}',
                    is_active BOOLEAN DEFAULT 1,
                    last_used TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # 创建内容记录表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS content_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id INTEGER NOT NULL,
                    source_content_id TEXT NOT NULL,
                    source_url TEXT,
                    target_content_id TEXT,
                    target_url TEXT,
                    title TEXT,
                    description TEXT,
                    file_path TEXT,
                    status TEXT DEFAULT 'downloaded',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (task_id) REFERENCES tasks (id)
                )
            ''')
            
            conn.commit()
            logger.info("数据库表初始化完成")
    
    # 任务相关操作
    def create_task(self, task: Task) -> int:
        """创建任务"""
        with self.get_connection() as conn:
            task_data = task.to_dict()
            task_data.pop('id', None)  # 移除ID，让数据库自动生成
            
            columns = ', '.join(task_data.keys())
            placeholders = ', '.join(['?' for _ in task_data])
            
            cursor = conn.execute(
                f'INSERT INTO tasks ({columns}) VALUES ({placeholders})',
                list(task_data.values())
            )
            conn.commit()
            
            task_id = cursor.lastrowid
            logger.info(f"创建任务成功: {task_id}")
            return task_id
    
    def get_task(self, task_id: int) -> Optional[Task]:
        """获取任务"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT * FROM tasks WHERE id = ?', (task_id,))
            row = cursor.fetchone()
            
            if row:
                return Task.from_dict(dict(row))
            return None
    
    def get_all_tasks(self) -> List[Task]:
        """获取所有任务"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT * FROM tasks ORDER BY created_at DESC')
            rows = cursor.fetchall()
            
            return [Task.from_dict(dict(row)) for row in rows]
    
    def get_active_tasks(self) -> List[Task]:
        """获取活跃任务"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                'SELECT * FROM tasks WHERE status IN (?, ?) ORDER BY created_at DESC',
                (TaskStatus.CREATED.value, TaskStatus.RUNNING.value)
            )
            rows = cursor.fetchall()
            
            return [Task.from_dict(dict(row)) for row in rows]
    
    def update_task(self, task: Task) -> bool:
        """更新任务"""
        if not task.id:
            return False
        
        with self.get_connection() as conn:
            task_data = task.to_dict()
            task_id = task_data.pop('id')
            
            set_clause = ', '.join([f'{key} = ?' for key in task_data.keys()])
            values = list(task_data.values()) + [task_id]
            
            cursor = conn.execute(
                f'UPDATE tasks SET {set_clause} WHERE id = ?',
                values
            )
            conn.commit()
            
            return cursor.rowcount > 0
    
    def delete_task(self, task_id: int) -> bool:
        """删除任务"""
        with self.get_connection() as conn:
            cursor = conn.execute('DELETE FROM tasks WHERE id = ?', (task_id,))
            conn.commit()

            return cursor.rowcount > 0

    # 账号相关操作
    def create_account(self, account: Account) -> int:
        """创建账号"""
        with self.get_connection() as conn:
            account_data = account.to_dict()
            account_data.pop('id', None)  # 移除ID，让数据库自动生成

            columns = ', '.join(account_data.keys())
            placeholders = ', '.join(['?' for _ in account_data])

            cursor = conn.execute(
                f'INSERT INTO accounts ({columns}) VALUES ({placeholders})',
                list(account_data.values())
            )
            conn.commit()

            account_id = cursor.lastrowid
            logger.info(f"创建账号成功: {account_id}")
            return account_id

    def get_account(self, account_id: int) -> Optional[Account]:
        """获取账号"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT * FROM accounts WHERE id = ?', (account_id,))
            row = cursor.fetchone()

            if row:
                return Account.from_dict(dict(row))
            return None

    def get_accounts_by_platform(self, platform: PlatformType, account_type: Optional[AccountType] = None) -> List[Account]:
        """根据平台获取账号"""
        with self.get_connection() as conn:
            if account_type:
                cursor = conn.execute(
                    'SELECT * FROM accounts WHERE platform = ? AND account_type IN (?, ?) AND is_active = 1',
                    (platform.value, account_type.value, AccountType.BOTH.value)
                )
            else:
                cursor = conn.execute(
                    'SELECT * FROM accounts WHERE platform = ? AND is_active = 1',
                    (platform.value,)
                )

            rows = cursor.fetchall()
            return [Account.from_dict(dict(row)) for row in rows]

    def get_all_accounts(self) -> List[Account]:
        """获取所有账号"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT * FROM accounts ORDER BY created_at DESC')
            rows = cursor.fetchall()

            return [Account.from_dict(dict(row)) for row in rows]

    def update_account(self, account: Account) -> bool:
        """更新账号"""
        if not account.id:
            return False

        with self.get_connection() as conn:
            account_data = account.to_dict()
            account_id = account_data.pop('id')

            set_clause = ', '.join([f'{key} = ?' for key in account_data.keys()])
            values = list(account_data.values()) + [account_id]

            cursor = conn.execute(
                f'UPDATE accounts SET {set_clause} WHERE id = ?',
                values
            )
            conn.commit()

            return cursor.rowcount > 0

    def delete_account(self, account_id: int) -> bool:
        """删除账号"""
        with self.get_connection() as conn:
            cursor = conn.execute('DELETE FROM accounts WHERE id = ?', (account_id,))
            conn.commit()

            return cursor.rowcount > 0

    def content_exists(self, content_id: str, task_id: str) -> bool:
        """检查内容是否已存在"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT 1 FROM content_records WHERE content_id = ? AND task_id = ?",
                    (content_id, task_id)
                )
                return cursor.fetchone() is not None
        except Exception as e:
            logger.error(f"检查内容存在性失败: {e}")
            return False

    def create_content_record(self,
                            content_id: str,
                            task_id: str,
                            title: str,
                            source_url: str,
                            local_paths: List[str],
                            metadata: Dict[str, Any]) -> bool:
        """创建内容记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO content_records
                    (content_id, task_id, title, source_url, local_paths, metadata, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    content_id,
                    task_id,
                    title,
                    source_url,
                    json.dumps(local_paths),
                    json.dumps(metadata, default=str),
                    datetime.now().isoformat()
                ))
                conn.commit()
                logger.debug(f"内容记录创建成功: {content_id}")
                return True
        except Exception as e:
            logger.error(f"创建内容记录失败: {e}")
            return False

    def update_content_record_upload_status(self,
                                          content_id: str,
                                          task_id: str,
                                          platform: 'PlatformType',
                                          success: bool,
                                          upload_url: str = "",
                                          upload_id: str = "",
                                          error_msg: str = "") -> bool:
        """更新内容记录的上传状态"""
        try:
            with self.get_connection() as conn:
                # 获取现有的上传状态
                cursor = conn.execute(
                    "SELECT upload_status FROM content_records WHERE content_id = ? AND task_id = ?",
                    (content_id, task_id)
                )
                row = cursor.fetchone()
                if not row:
                    logger.error(f"内容记录不存在: {content_id}")
                    return False

                # 解析现有状态
                upload_status = json.loads(row[0]) if row[0] else {}

                # 更新平台状态
                upload_status[platform.value] = {
                    "success": success,
                    "upload_url": upload_url,
                    "upload_id": upload_id,
                    "error_msg": error_msg,
                    "updated_at": datetime.now().isoformat()
                }

                # 保存更新
                cursor = conn.execute("""
                    UPDATE content_records
                    SET upload_status = ?, updated_at = ?
                    WHERE content_id = ? AND task_id = ?
                """, (
                    json.dumps(upload_status),
                    datetime.now().isoformat(),
                    content_id,
                    task_id
                ))
                conn.commit()

                logger.debug(f"上传状态更新成功: {content_id} -> {platform.value}")
                return True

        except Exception as e:
            logger.error(f"更新上传状态失败: {e}")
            return False

    def close(self):
        """关闭数据库连接"""
        if hasattr(self, '_connection') and self._connection:
            self._connection.close()
            logger.info("数据库连接已关闭")
