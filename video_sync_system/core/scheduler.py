"""
任务调度器
"""

import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.memory import MemoryJobS<PERSON>
from apscheduler.executors.asyncio import AsyncIOExecutor

from .task_manager import TaskManager
from .database import Database
from models.task import Task, TaskStatus
from utils.logger import get_logger
from utils.config import config

logger = get_logger(__name__)


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, task_manager: TaskManager):
        self.task_manager = task_manager
        self.db = Database()
        
        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 3
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone=config.get_scheduler_config()['timezone']
        )
        
        self.running = False
        self.scheduled_tasks = {}  # task_id -> job_id 映射
    
    async def start(self):
        """启动调度器"""
        if self.running:
            logger.warning("调度器已在运行")
            return
        
        try:
            self.scheduler.start()
            self.running = True
            
            # 加载现有任务
            await self._load_existing_tasks()
            
            # 添加系统维护任务
            await self._add_system_tasks()
            
            logger.info("任务调度器启动成功")
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            raise
    
    async def stop(self):
        """停止调度器"""
        if not self.running:
            return
        
        try:
            self.scheduler.shutdown(wait=True)
            self.running = False
            self.scheduled_tasks.clear()
            
            logger.info("任务调度器已停止")
            
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    async def add_task(self, task: Task) -> bool:
        """添加任务到调度器"""
        if not self.running:
            logger.error("调度器未运行")
            return False
        
        try:
            # 如果任务已存在，先移除
            if task.id in self.scheduled_tasks:
                await self.remove_task(task.id)
            
            # 只调度运行状态的任务
            if task.status != TaskStatus.RUNNING:
                logger.debug(f"任务状态不是运行中，跳过调度: {task.name}")
                return True
            
            # 创建调度任务
            job_id = f"task_{task.id}"
            
            # 使用间隔触发器
            trigger = IntervalTrigger(seconds=task.check_interval)
            
            # 添加任务到调度器
            self.scheduler.add_job(
                func=self._execute_scheduled_task,
                trigger=trigger,
                args=[task.id],
                id=job_id,
                name=f"Task: {task.name}",
                replace_existing=True,
                next_run_time=self._calculate_next_run_time(task)
            )
            
            self.scheduled_tasks[task.id] = job_id
            logger.info(f"任务已添加到调度器: {task.name} (间隔: {task.check_interval}秒)")
            
            return True
            
        except Exception as e:
            logger.error(f"添加任务到调度器失败: {task.name}, 错误: {e}")
            return False
    
    async def remove_task(self, task_id: str) -> bool:
        """从调度器移除任务"""
        try:
            if task_id in self.scheduled_tasks:
                job_id = self.scheduled_tasks[task_id]
                self.scheduler.remove_job(job_id)
                del self.scheduled_tasks[task_id]
                logger.info(f"任务已从调度器移除: {task_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"从调度器移除任务失败: {task_id}, 错误: {e}")
            return False
    
    async def update_task(self, task: Task) -> bool:
        """更新调度器中的任务"""
        try:
            # 先移除旧任务
            await self.remove_task(task.id)
            
            # 添加新任务
            return await self.add_task(task)
            
        except Exception as e:
            logger.error(f"更新调度器任务失败: {task.name}, 错误: {e}")
            return False
    
    async def get_scheduled_tasks(self) -> List[Dict]:
        """获取已调度的任务信息"""
        try:
            jobs = self.scheduler.get_jobs()
            task_info = []
            
            for job in jobs:
                if job.id.startswith('task_'):
                    task_id = job.id.replace('task_', '')
                    task_info.append({
                        'task_id': task_id,
                        'job_id': job.id,
                        'name': job.name,
                        'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                        'trigger': str(job.trigger)
                    })
            
            return task_info
            
        except Exception as e:
            logger.error(f"获取调度任务信息失败: {e}")
            return []
    
    async def _load_existing_tasks(self):
        """加载现有任务"""
        try:
            tasks = self.db.get_all_tasks()
            loaded_count = 0
            
            for task in tasks:
                if task.status == TaskStatus.RUNNING:
                    success = await self.add_task(task)
                    if success:
                        loaded_count += 1
            
            logger.info(f"加载现有任务完成: {loaded_count} 个任务")
            
        except Exception as e:
            logger.error(f"加载现有任务失败: {e}")
    
    async def _add_system_tasks(self):
        """添加系统维护任务"""
        try:
            # 添加清理任务 - 每天凌晨2点执行
            self.scheduler.add_job(
                func=self._cleanup_old_files,
                trigger=CronTrigger(hour=2, minute=0),
                id='system_cleanup',
                name='System Cleanup',
                replace_existing=True
            )
            
            # 添加状态检查任务 - 每10分钟执行
            self.scheduler.add_job(
                func=self._check_task_status,
                trigger=IntervalTrigger(minutes=10),
                id='status_check',
                name='Status Check',
                replace_existing=True
            )
            
            logger.info("系统维护任务已添加")
            
        except Exception as e:
            logger.error(f"添加系统任务失败: {e}")
    
    async def _execute_scheduled_task(self, task_id: str):
        """执行调度的任务"""
        try:
            logger.debug(f"执行调度任务: {task_id}")
            
            # 获取任务信息
            task = self.db.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                await self.remove_task(task_id)
                return
            
            # 检查任务状态
            if task.status != TaskStatus.RUNNING:
                logger.info(f"任务状态已变更，停止调度: {task.name}")
                await self.remove_task(task_id)
                return
            
            # 执行任务
            await self.task_manager._run_task_once(task)
            
            # 更新任务信息
            task.last_run = datetime.now()
            task.next_run = datetime.now() + timedelta(seconds=task.check_interval)
            task.success_count += 1
            task.last_error = None
            
            self.db.update_task(task)
            
        except Exception as e:
            logger.error(f"执行调度任务失败: {task_id}, 错误: {e}")
            
            # 更新错误信息
            try:
                task = self.db.get_task(task_id)
                if task:
                    task.error_count += 1
                    task.last_error = str(e)
                    
                    # 如果错误次数过多，暂停任务
                    if task.error_count >= 5:
                        task.status = TaskStatus.FAILED
                        await self.remove_task(task_id)
                        logger.error(f"任务失败次数过多，已停止调度: {task.name}")
                    
                    self.db.update_task(task)
            except Exception as update_error:
                logger.error(f"更新任务错误信息失败: {update_error}")
    
    async def _cleanup_old_files(self):
        """清理旧文件"""
        try:
            logger.info("开始清理旧文件")
            
            from ..utils.file_utils import FileUtils
            
            # 清理下载目录
            download_path = config.get("DOWNLOAD_PATH", "data/downloads")
            deleted_count = FileUtils.clean_directory(download_path, max_age_days=7)
            
            # 清理日志文件
            log_path = config.get("LOG_FILE", "data/logs/app.log")
            log_dir = os.path.dirname(log_path)
            if os.path.exists(log_dir):
                FileUtils.clean_directory(log_dir, max_age_days=30)
            
            logger.info(f"文件清理完成，删除文件数: {deleted_count}")
            
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
    
    async def _check_task_status(self):
        """检查任务状态"""
        try:
            logger.debug("检查任务状态")
            
            # 获取所有运行中的任务
            tasks = self.db.get_all_tasks()
            
            for task in tasks:
                if task.status == TaskStatus.RUNNING:
                    # 检查任务是否在调度器中
                    if task.id not in self.scheduled_tasks:
                        logger.warning(f"运行中的任务未在调度器中，重新添加: {task.name}")
                        await self.add_task(task)
                    
                    # 检查任务是否长时间未运行
                    if task.last_run:
                        time_since_last_run = datetime.now() - task.last_run
                        max_interval = timedelta(seconds=task.check_interval * 2)  # 允许2倍间隔的延迟
                        
                        if time_since_last_run > max_interval:
                            logger.warning(f"任务长时间未运行: {task.name}, 上次运行: {task.last_run}")
            
        except Exception as e:
            logger.error(f"检查任务状态失败: {e}")
    
    def _calculate_next_run_time(self, task: Task) -> Optional[datetime]:
        """计算下次运行时间"""
        if task.next_run and task.next_run > datetime.now():
            return task.next_run
        
        # 如果没有设置下次运行时间或已过期，立即运行
        return datetime.now() + timedelta(seconds=5)  # 5秒后开始
