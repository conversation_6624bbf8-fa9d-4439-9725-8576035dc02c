#!/usr/bin/env python3
"""
简化的演示数据创建脚本
"""

import sys
import sqlite3
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database import Database
from models.account import Account, PlatformType, AccountType
from models.task import Task, TaskStatus


def create_simple_demo_data():
    """创建简化的演示数据"""
    print("=" * 60)
    print("创建简化演示数据")
    print("=" * 60)
    
    # 初始化数据库
    db = Database()
    
    # 创建演示账号
    print("创建演示账号...")
    accounts = [
        Account(
            name='小红书监控账号',
            platform=PlatformType.XIAOHONGSHU,
            account_type=AccountType.MONITOR,
            username='demo_xhs_user',
            user_id='xhs_123456',
            cookies='demo_cookies_xhs',
            config={'proxy': None},
            is_active=True
        ),
        Account(
            name='TikTok上传账号',
            platform=PlatformType.TIKTOK,
            account_type=AccountType.UPLOAD,
            username='demo_tiktok_user',
            user_id='tiktok_789012',
            cookies='demo_cookies_tiktok',
            config={'quality': 'HD'},
            is_active=True
        ),
        Account(
            name='YouTube上传账号',
            platform=PlatformType.YOUTUBE,
            account_type=AccountType.UPLOAD,
            username='demo_youtube_user',
            user_id='youtube_345678',
            access_token='demo_access_token',
            config={'channel_id': 'UC123456789'},
            is_active=True
        )
    ]
    
    account_ids = {}
    for account in accounts:
        try:
            account_id = db.create_account(account)
            account_ids[f"{account.platform.value}_{account.account_type.value}"] = account_id
            print(f"✓ 创建账号: {account.name} (ID: {account_id})")
        except Exception as e:
            print(f"✗ 创建账号失败 {account.name}: {e}")
    
    # 创建演示任务
    print("\n创建演示任务...")
    
    # 获取账号ID
    xhs_monitor_id = account_ids.get('xiaohongshu_monitor', 1)
    tiktok_upload_id = account_ids.get('tiktok_upload', 2)
    youtube_upload_id = account_ids.get('youtube_upload', 3)
    
    tasks = [
        Task(
            name='小红书到TikTok同步',
            description='监控小红书美食博主，自动同步到TikTok',
            source_platform=PlatformType.XIAOHONGSHU,
            target_platform=PlatformType.TIKTOK,
            source_account_id=xhs_monitor_id,
            target_account_id=tiktok_upload_id,
            check_interval=3600,
            max_downloads=10,
            auto_upload=True,
            status=TaskStatus.CREATED,
            config={'keywords': ['美食', '教程']}
        ),
        Task(
            name='小红书到YouTube同步',
            description='监控小红书旅游博主，自动同步到YouTube',
            source_platform=PlatformType.XIAOHONGSHU,
            target_platform=PlatformType.YOUTUBE,
            source_account_id=xhs_monitor_id,
            target_account_id=youtube_upload_id,
            check_interval=7200,
            max_downloads=5,
            auto_upload=False,
            status=TaskStatus.RUNNING,
            config={'keywords': ['旅游', '攻略']}
        )
    ]
    
    for task in tasks:
        try:
            task_id = db.create_task(task)
            print(f"✓ 创建任务: {task.name} (ID: {task_id})")
        except Exception as e:
            print(f"✗ 创建任务失败 {task.name}: {e}")
    
    print("\n" + "=" * 60)
    print("演示数据创建完成！")
    print("现在可以启动Web服务器查看效果：")
    print("python run_web.py --host 0.0.0.0 --port 8080")
    print("=" * 60)


if __name__ == '__main__':
    create_simple_demo_data()
