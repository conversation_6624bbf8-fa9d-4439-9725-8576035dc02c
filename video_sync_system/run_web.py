#!/usr/bin/env python3
"""
视频同步系统 Web 服务器启动脚本
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web_server import WebServer
from core.database import Database


def setup_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    db = Database()
    print("数据库初始化完成")


def setup_directories():
    """创建必要的目录"""
    directories = [
        'data',
        'data/logs',
        'data/downloads',
        'data/uploads',
        'data/temp',
        'data/configs'
    ]

    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"确保目录存在: {dir_path}")


def main():
    parser = argparse.ArgumentParser(description='视频同步系统 Web 服务器')
    parser.add_argument('--host', default='127.0.0.1', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--init-db', action='store_true', help='初始化数据库')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("视频同步系统 Web 服务器")
    print("=" * 60)
    
    # 设置环境变量
    if args.debug:
        os.environ['FLASK_ENV'] = 'development'
        os.environ['FLASK_DEBUG'] = '1'
    
    # 创建必要的目录
    setup_directories()
    
    # 初始化数据库（如果需要）
    if args.init_db:
        setup_database()

    # 创建Web服务器
    web_server = WebServer(host=args.host, port=args.port)

    print(f"服务器将在 http://{args.host}:{args.port} 启动")
    print("按 Ctrl+C 停止服务器")
    print("-" * 60)

    try:
        # 启动服务器
        web_server.run(debug=args.debug)
    except KeyboardInterrupt:
        print("\n正在停止服务器...")
    except Exception as e:
        print(f"服务器启动失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
