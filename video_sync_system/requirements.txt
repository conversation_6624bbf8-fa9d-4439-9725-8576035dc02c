# 核心依赖
requests>=2.31.0
aiohttp>=3.8.0
asyncio-throttle>=1.0.2

# 数据库
# sqlite3 是Python内置模块

# 任务调度
apscheduler>=3.10.0

# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0

# 日志
loguru>=0.7.0

# 文件处理
# pathlib 和 shutil 是Python内置模块

# 网络请求增强
httpx>=0.24.0
urllib3>=2.0.0

# 数据处理
pandas>=2.0.0
openpyxl>=3.1.0

# 媒体处理
pillow>=10.0.0  # 图片处理
moviepy>=1.0.3  # 视频处理

# 用户代理
fake-useragent>=1.4.0

# 浏览器自动化 (用于某些平台的登录和上传)
playwright>=1.40.0
selenium>=4.15.0

# 加密
cryptography>=41.0.0

# 重试机制
tenacity>=8.2.0

# 命令行界面
click>=8.1.0
rich>=13.0.0

# 现有代码依赖 (从legacy代码中提取)
PyExecJS>=1.5.1
retry>=0.9.2

# Web界面 (可选)
flask>=2.3.0
flask-cors>=4.0.0

# 开发依赖
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
