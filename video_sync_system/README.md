# 视频同步系统 (Video Sync System)

一个多平台视频监控和自动上传系统，支持从小红书、抖音、B站等平台监控内容，并自动同步到目标平台。

## 功能特性

- **多平台支持**: 小红书、抖音、TikTok、B站、YouTube
- **任务驱动**: 基于任务的监控和上传管理
- **自动调度**: 定时检查新内容并自动处理
- **账号管理**: 支持多账号管理和切换
- **错误处理**: 完善的错误处理和重试机制
- **日志记录**: 详细的操作日志和状态跟踪

## 安装和配置

### 1. 环境要求

- Python 3.8+
- 操作系统: Windows/Linux/macOS

### 2. 安装依赖

```bash
cd video_sync_system
pip install -r requirements.txt
```

### 3. 安装浏览器驱动 (用于Playwright)

```bash
playwright install
```

### 4. 配置环境变量

创建 `.env` 文件:

```env
# 数据库配置
DATABASE_PATH=data/database.db

# 下载配置
DOWNLOAD_PATH=data/downloads
MAX_DOWNLOAD_SIZE=100MB

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=data/logs/app.log

# 调度器配置
SCHEDULER_TIMEZONE=Asia/Shanghai
MAX_CONCURRENT_TASKS=5
```

## 项目结构

```
video_sync_system/
├── main.py                 # 应用入口
├── core/                   # 核心模块
├── platforms/              # 平台适配器
├── models/                 # 数据模型
├── utils/                  # 工具函数
└── data/                   # 数据存储
```

## 使用指南

### 1. 启动守护进程

```bash
python run.py daemon
```

### 2. 账号管理

#### 创建监控账号

```bash
python run.py account create \
  --name "小红书监控账号1" \
  --platform xiaohongshu \
  --type monitor \
  --cookies "your_cookies_here"
```

#### 创建上传账号

```bash
python run.py account create \
  --name "小红书上传账号1" \
  --platform xiaohongshu \
  --type upload \
  --cookies "your_upload_cookies_here"
```

#### 查看所有账号

```bash
python run.py account list
```

### 3. 任务管理

#### 创建同步任务

```bash
python run.py task create \
  --name "小红书到抖音同步" \
  --source-platform xiaohongshu \
  --target-platforms douyin tiktok \
  --source-user-id "user123" \
  --monitor-account-id 1 \
  --upload-account-ids '{"douyin": 2, "tiktok": 3}' \
  --check-interval 3600 \
  --download-limit 10
```

#### 启动任务

```bash
python run.py task start <task_id>
```

#### 停止任务

```bash
python run.py task stop <task_id>
```

#### 查看所有任务

```bash
python run.py task list
```

## 平台支持状态

| 平台 | 监控 | 上传 | 状态 |
|------|------|------|------|
| 小红书 | ✅ | ✅ | 完整支持 |
| 抖音 | 🚧 | 🚧 | 开发中 |
| TikTok | 🚧 | 🚧 | 开发中 |
| B站 | 🚧 | 🚧 | 开发中 |
| YouTube | 🚧 | 🚧 | 开发中 |

## 开发状态

- [x] 项目基础框架
- [x] 核心数据模型
- [x] 基础工具模块
- [x] 平台适配器基类
- [x] 小红书适配器实现
- [x] 任务管理器
- [x] 调度器
- [x] 命令行界面

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
