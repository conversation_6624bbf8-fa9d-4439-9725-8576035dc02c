{% extends "base.html" %}

{% block title %}仪表板 - 视频同步系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 系统概览卡片 -->
    <div class="col-md-3 mb-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="total-accounts">0</h4>
                        <p class="mb-0">总账号数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="total-tasks">0</h4>
                        <p class="mb-0">总任务数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tasks fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="running-tasks">0</h4>
                        <p class="mb-0">运行中任务</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-play fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="scheduler-status">停止</h4>
                        <p class="mb-0">调度器状态</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 任务状态图表 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i> 任务状态分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="taskStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 平台分布图表 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i> 平台分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="platformChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近任务活动 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i> 最近任务活动
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>状态</th>
                                <th>最后运行</th>
                                <th>成功次数</th>
                                <th>错误次数</th>
                            </tr>
                        </thead>
                        <tbody id="recent-tasks">
                            <tr>
                                <td colspan="5" class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 系统信息
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <strong>系统状态:</strong>
                        <span id="system-info-status" class="badge bg-success">运行中</span>
                    </li>
                    <li class="mb-2">
                        <strong>运行时间:</strong>
                        <span id="uptime">--</span>
                    </li>
                    <li class="mb-2">
                        <strong>最后更新:</strong>
                        <span id="last-update">--</span>
                    </li>
                </ul>
                
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> 刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统资源监控 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i> 系统资源使用率
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <canvas id="systemChart" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server"></i> 系统指标
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">CPU使用率</small>
                        <small id="cpu-usage" class="fw-bold">0%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div id="cpu-progress" class="progress-bar bg-success" style="width: 0%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">内存使用率</small>
                        <small id="memory-usage" class="fw-bold">0%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div id="memory-progress" class="progress-bar bg-info" style="width: 0%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">磁盘使用率</small>
                        <small id="disk-usage" class="fw-bold">0%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div id="disk-progress" class="progress-bar bg-primary" style="width: 0%"></div>
                    </div>
                </div>

                <div class="mb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">网络状态</small>
                        <div id="network-status">
                            <span class="badge bg-secondary">
                                <i class="fas fa-question"></i> 未知
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实时日志 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-terminal"></i> 实时日志
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">
                        <i class="fas fa-trash"></i> 清空
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="toggleAutoScroll()">
                        <i class="fas fa-arrow-down"></i> 自动滚动
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="log-container" class="bg-dark text-light p-3" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9em;">
                    <div class="text-muted">等待日志数据...</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面特定的JavaScript代码
let taskStatusChart, platformChart;
let autoScroll = true;

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    initCharts();
    loadDashboardData();
    
    // 每30秒刷新一次数据
    setInterval(loadDashboardData, 30000);
});

// 初始化图表
function initCharts() {
    // 任务状态饼图
    const taskCtx = document.getElementById('taskStatusChart').getContext('2d');
    taskStatusChart = new Chart(taskCtx, {
        type: 'doughnut',
        data: {
            labels: ['运行中', '已暂停', '已完成', '失败'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: ['#28a745', '#ffc107', '#17a2b8', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // 平台分布柱状图
    const platformCtx = document.getElementById('platformChart').getContext('2d');
    platformChart = new Chart(platformCtx, {
        type: 'bar',
        data: {
            labels: ['小红书', '抖音', 'TikTok', 'B站', 'YouTube'],
            datasets: [{
                label: '账号数量',
                data: [0, 0, 0, 0, 0],
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 加载仪表板数据
function loadDashboardData() {
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            updateSystemStatus(data);
        })
        .catch(error => {
            console.error('加载系统状态失败:', error);
        });
    
    // 加载任务列表
    fetch('/api/tasks')
        .then(response => response.json())
        .then(data => {
            updateTasksTable(data);
            updateTaskStatusChart(data);
        })
        .catch(error => {
            console.error('加载任务列表失败:', error);
        });
    
    // 加载账号列表
    fetch('/api/accounts')
        .then(response => response.json())
        .then(data => {
            updatePlatformChart(data);
        })
        .catch(error => {
            console.error('加载账号列表失败:', error);
        });
}

// 更新系统状态
function updateSystemStatus(data) {
    document.getElementById('total-accounts').textContent = data.total_accounts;
    document.getElementById('total-tasks').textContent = data.total_tasks;
    document.getElementById('running-tasks').textContent = data.running_tasks;
    document.getElementById('scheduler-status').textContent = data.scheduler_status === 'running' ? '运行中' : '已停止';
    document.getElementById('uptime').textContent = data.uptime;
    document.getElementById('last-update').textContent = new Date().toLocaleString();
    
    // 更新状态指示器
    const statusElement = document.getElementById('system-status');
    if (data.system_status === 'running') {
        statusElement.className = 'badge bg-success';
        statusElement.innerHTML = '<i class="fas fa-circle"></i> 运行中';
    } else {
        statusElement.className = 'badge bg-danger';
        statusElement.innerHTML = '<i class="fas fa-circle"></i> 已停止';
    }
}

// 更新任务表格
function updateTasksTable(tasks) {
    const tbody = document.getElementById('recent-tasks');
    if (tasks.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无任务</td></tr>';
        return;
    }
    
    const recentTasks = tasks.slice(0, 5); // 只显示最近5个任务
    tbody.innerHTML = recentTasks.map(task => `
        <tr>
            <td>${task.name}</td>
            <td><span class="badge bg-${getStatusColor(task.status)}">${getStatusText(task.status)}</span></td>
            <td>${task.last_run ? new Date(task.last_run).toLocaleString() : '--'}</td>
            <td>${task.success_count || 0}</td>
            <td>${task.error_count || 0}</td>
        </tr>
    `).join('');
}

// 更新任务状态图表
function updateTaskStatusChart(tasks) {
    const statusCounts = {
        'running': 0,
        'paused': 0,
        'completed': 0,
        'failed': 0
    };
    
    tasks.forEach(task => {
        statusCounts[task.status] = (statusCounts[task.status] || 0) + 1;
    });
    
    taskStatusChart.data.datasets[0].data = [
        statusCounts.running,
        statusCounts.paused,
        statusCounts.completed,
        statusCounts.failed
    ];
    taskStatusChart.update();
}

// 更新平台图表
function updatePlatformChart(accounts) {
    const platformCounts = {
        'xiaohongshu': 0,
        'douyin': 0,
        'tiktok': 0,
        'bilibili': 0,
        'youtube': 0
    };
    
    accounts.forEach(account => {
        platformCounts[account.platform] = (platformCounts[account.platform] || 0) + 1;
    });
    
    platformChart.data.datasets[0].data = [
        platformCounts.xiaohongshu,
        platformCounts.douyin,
        platformCounts.tiktok,
        platformCounts.bilibili,
        platformCounts.youtube
    ];
    platformChart.update();
}

// 工具函数
function getStatusColor(status) {
    const colors = {
        'running': 'success',
        'paused': 'warning',
        'completed': 'info',
        'failed': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'running': '运行中',
        'paused': '已暂停',
        'completed': '已完成',
        'failed': '失败'
    };
    return texts[status] || status;
}

function refreshData() {
    loadDashboardData();
    showMessage('数据已刷新', 'success');
}

function clearLogs() {
    document.getElementById('log-container').innerHTML = '<div class="text-muted">日志已清空</div>';
}

function toggleAutoScroll() {
    autoScroll = !autoScroll;
    const btn = event.target.closest('button');
    if (autoScroll) {
        btn.innerHTML = '<i class="fas fa-arrow-down"></i> 自动滚动';
        btn.classList.remove('active');
    } else {
        btn.innerHTML = '<i class="fas fa-pause"></i> 已暂停';
        btn.classList.add('active');
    }
}
</script>
{% endblock %}
