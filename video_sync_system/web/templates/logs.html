{% extends "base.html" %}

{% block title %}日志查看 - 视频同步系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-file-alt"></i> 日志查看</h2>
    <div>
        <button class="btn btn-outline-secondary" onclick="clearLogs()">
            <i class="fas fa-trash"></i> 清空日志
        </button>
        <button class="btn btn-outline-primary" onclick="downloadLogs()">
            <i class="fas fa-download"></i> 下载日志
        </button>
    </div>
</div>

<!-- 日志控制面板 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-3">
                <label for="logLevel" class="form-label">日志级别</label>
                <select class="form-select" id="logLevel">
                    <option value="">所有级别</option>
                    <option value="DEBUG">调试</option>
                    <option value="INFO">信息</option>
                    <option value="WARNING">警告</option>
                    <option value="ERROR">错误</option>
                    <option value="CRITICAL">严重</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="logSource" class="form-label">日志来源</label>
                <select class="form-select" id="logSource">
                    <option value="">所有来源</option>
                    <option value="task_manager">任务管理器</option>
                    <option value="scheduler">调度器</option>
                    <option value="platform">平台适配器</option>
                    <option value="web_server">Web服务器</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="searchLogs" class="form-label">搜索关键词</label>
                <input type="text" class="form-control" id="searchLogs" placeholder="搜索日志内容...">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button class="btn btn-primary" onclick="filterLogs()">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="autoScroll" checked>
                    <label class="form-check-label" for="autoScroll">
                        自动滚动到底部
                    </label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="realTimeUpdate" checked>
                    <label class="form-check-label" for="realTimeUpdate">
                        实时更新日志
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="info-count">0</h4>
                        <p class="mb-0">信息</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-info-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="warning-count">0</h4>
                        <p class="mb-0">警告</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="error-count">0</h4>
                        <p class="mb-0">错误</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="total-logs-count">0</h4>
                        <p class="mb-0">总计</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实时日志显示 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-terminal"></i> 实时日志
            <span id="connection-status" class="badge bg-success ms-2">
                <i class="fas fa-circle"></i> 已连接
            </span>
        </h5>
        <div>
            <span class="text-muted">显示最近 </span>
            <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="maxLogEntries">
                <option value="100">100</option>
                <option value="500" selected>500</option>
                <option value="1000">1000</option>
                <option value="2000">2000</option>
            </select>
            <span class="text-muted"> 条日志</span>
        </div>
    </div>
    <div class="card-body p-0">
        <div id="log-display" class="bg-dark text-light p-3" style="height: 600px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 0.9em; line-height: 1.4;">
            <div class="text-muted text-center py-4">
                <i class="fas fa-spinner fa-spin"></i> 正在连接日志服务...
            </div>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <small class="text-muted">
                    <i class="fas fa-clock"></i> 最后更新: <span id="last-log-time">--</span>
                </small>
            </div>
            <div>
                <button class="btn btn-sm btn-outline-secondary" onclick="pauseLogStream()">
                    <i class="fas fa-pause"></i> 暂停
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="resumeLogStream()">
                    <i class="fas fa-play"></i> 继续
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="scrollToBottom()">
                    <i class="fas fa-arrow-down"></i> 滚动到底部
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 历史日志查询模态框 -->
<div class="modal fade" id="historyLogsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">历史日志查询</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="startDate" class="form-label">开始日期</label>
                        <input type="datetime-local" class="form-control" id="startDate">
                    </div>
                    <div class="col-md-4">
                        <label for="endDate" class="form-label">结束日期</label>
                        <input type="datetime-local" class="form-control" id="endDate">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-primary" onclick="queryHistoryLogs()">
                                <i class="fas fa-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="history-logs-result" class="bg-dark text-light p-3" style="height: 400px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 0.9em;">
                    <div class="text-muted text-center py-4">
                        请选择日期范围并点击查询按钮
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportHistoryLogs()">
                    <i class="fas fa-download"></i> 导出结果
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let logEntries = [];
let filteredLogEntries = [];
let logStreamPaused = false;
let maxLogEntries = 500;
let logCounts = {
    INFO: 0,
    WARNING: 0,
    ERROR: 0,
    CRITICAL: 0,
    DEBUG: 0
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeLogViewer();
    setupEventListeners();
});

// 初始化日志查看器
function initializeLogViewer() {
    // 设置WebSocket日志监听
    if (typeof socket !== 'undefined') {
        socket.on('log_message', function(data) {
            if (!logStreamPaused && document.getElementById('realTimeUpdate').checked) {
                addLogEntry(data);
            }
        });
        
        socket.on('connect', function() {
            updateConnectionStatus(true);
        });
        
        socket.on('disconnect', function() {
            updateConnectionStatus(false);
        });
    }
    
    // 加载初始日志
    loadRecentLogs();
}

// 设置事件监听器
function setupEventListeners() {
    // 日志级别筛选
    document.getElementById('logLevel').addEventListener('change', filterLogs);
    document.getElementById('logSource').addEventListener('change', filterLogs);
    
    // 搜索输入
    document.getElementById('searchLogs').addEventListener('input', debounce(filterLogs, 300));
    
    // 最大日志条数变化
    document.getElementById('maxLogEntries').addEventListener('change', function() {
        maxLogEntries = parseInt(this.value);
        trimLogEntries();
        displayLogs();
    });
    
    // 自动滚动开关
    document.getElementById('autoScroll').addEventListener('change', function() {
        if (this.checked) {
            scrollToBottom();
        }
    });
}

// 加载最近的日志
function loadRecentLogs() {
    // 这里可以通过API加载历史日志
    // 暂时显示连接成功消息
    const welcomeLog = {
        timestamp: new Date().toISOString(),
        level: 'INFO',
        source: 'web_server',
        message: '日志查看器已连接，等待实时日志数据...'
    };
    addLogEntry(welcomeLog);
}

// 添加日志条目
function addLogEntry(logData) {
    const logEntry = {
        timestamp: logData.timestamp || new Date().toISOString(),
        level: logData.level || 'INFO',
        source: logData.source || 'unknown',
        message: logData.message || ''
    };
    
    logEntries.push(logEntry);
    
    // 更新统计
    logCounts[logEntry.level] = (logCounts[logEntry.level] || 0) + 1;
    updateLogStats();
    
    // 限制日志条数
    trimLogEntries();
    
    // 应用筛选并显示
    filterLogs();
    
    // 更新最后日志时间
    document.getElementById('last-log-time').textContent = new Date().toLocaleTimeString();
}

// 限制日志条数
function trimLogEntries() {
    if (logEntries.length > maxLogEntries) {
        const removed = logEntries.splice(0, logEntries.length - maxLogEntries);
        // 更新统计计数
        removed.forEach(entry => {
            logCounts[entry.level] = Math.max(0, (logCounts[entry.level] || 0) - 1);
        });
        updateLogStats();
    }
}

// 筛选日志
function filterLogs() {
    const levelFilter = document.getElementById('logLevel').value;
    const sourceFilter = document.getElementById('logSource').value;
    const searchTerm = document.getElementById('searchLogs').value.toLowerCase();
    
    filteredLogEntries = logEntries.filter(entry => {
        const matchesLevel = !levelFilter || entry.level === levelFilter;
        const matchesSource = !sourceFilter || entry.source.includes(sourceFilter);
        const matchesSearch = !searchTerm || 
                            entry.message.toLowerCase().includes(searchTerm) ||
                            entry.source.toLowerCase().includes(searchTerm);
        
        return matchesLevel && matchesSource && matchesSearch;
    });
    
    displayLogs();
}

// 显示日志
function displayLogs() {
    const logDisplay = document.getElementById('log-display');
    
    if (filteredLogEntries.length === 0) {
        logDisplay.innerHTML = '<div class="text-muted text-center py-4">没有匹配的日志条目</div>';
        return;
    }
    
    const logsHtml = filteredLogEntries.map(entry => {
        const timestamp = new Date(entry.timestamp).toLocaleString();
        const levelClass = getLevelClass(entry.level);
        
        return `
            <div class="log-entry ${levelClass}" data-level="${entry.level}">
                <span class="log-timestamp text-muted">[${timestamp}]</span>
                <span class="log-level badge bg-${getLevelColor(entry.level)}">${entry.level}</span>
                <span class="log-source text-info">${entry.source}:</span>
                <span class="log-message">${escapeHtml(entry.message)}</span>
            </div>
        `;
    }).join('');
    
    logDisplay.innerHTML = logsHtml;
    
    // 自动滚动到底部
    if (document.getElementById('autoScroll').checked) {
        scrollToBottom();
    }
}

// 更新日志统计
function updateLogStats() {
    document.getElementById('info-count').textContent = logCounts.INFO || 0;
    document.getElementById('warning-count').textContent = logCounts.WARNING || 0;
    document.getElementById('error-count').textContent = logCounts.ERROR || 0;
    
    const total = Object.values(logCounts).reduce((sum, count) => sum + (count || 0), 0);
    document.getElementById('total-logs-count').textContent = total;
}

// 更新连接状态
function updateConnectionStatus(connected) {
    const statusElement = document.getElementById('connection-status');
    if (connected) {
        statusElement.className = 'badge bg-success ms-2';
        statusElement.innerHTML = '<i class="fas fa-circle"></i> 已连接';
    } else {
        statusElement.className = 'badge bg-danger ms-2';
        statusElement.innerHTML = '<i class="fas fa-circle"></i> 连接断开';
    }
}

// 日志操作函数
function clearLogs() {
    showConfirmDialog(
        '确认清空日志',
        '确定要清空所有显示的日志吗？此操作不可撤销。',
        function() {
            logEntries = [];
            filteredLogEntries = [];
            logCounts = { INFO: 0, WARNING: 0, ERROR: 0, CRITICAL: 0, DEBUG: 0 };
            updateLogStats();
            displayLogs();
            showMessage('日志已清空', 'success');
        }
    );
}

function downloadLogs() {
    if (filteredLogEntries.length === 0) {
        showMessage('没有日志可以下载', 'warning');
        return;
    }
    
    const logText = filteredLogEntries.map(entry => {
        const timestamp = new Date(entry.timestamp).toLocaleString();
        return `[${timestamp}] ${entry.level} ${entry.source}: ${entry.message}`;
    }).join('\n');
    
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showMessage('日志文件已下载', 'success');
}

function pauseLogStream() {
    logStreamPaused = true;
    showMessage('日志流已暂停', 'info');
}

function resumeLogStream() {
    logStreamPaused = false;
    showMessage('日志流已恢复', 'success');
}

function scrollToBottom() {
    const logDisplay = document.getElementById('log-display');
    logDisplay.scrollTop = logDisplay.scrollHeight;
}

// 工具函数
function getLevelClass(level) {
    const classes = {
        'DEBUG': 'log-debug',
        'INFO': 'log-info',
        'WARNING': 'log-warning',
        'ERROR': 'log-error',
        'CRITICAL': 'log-error'
    };
    return classes[level] || '';
}

function getLevelColor(level) {
    const colors = {
        'DEBUG': 'secondary',
        'INFO': 'info',
        'WARNING': 'warning',
        'ERROR': 'danger',
        'CRITICAL': 'danger'
    };
    return colors[level] || 'secondary';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 历史日志查询功能
function queryHistoryLogs() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (!startDate || !endDate) {
        showMessage('请选择开始和结束日期', 'warning');
        return;
    }
    
    const resultDiv = document.getElementById('history-logs-result');
    resultDiv.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin"></i> 查询中...</div>';
    
    // 这里应该调用API查询历史日志
    // 暂时显示模拟数据
    setTimeout(() => {
        resultDiv.innerHTML = '<div class="text-muted text-center py-4">历史日志查询功能开发中...</div>';
    }, 1000);
}

function exportHistoryLogs() {
    showMessage('历史日志导出功能开发中...', 'info');
}
</script>
{% endblock %}
