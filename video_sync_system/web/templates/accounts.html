{% extends "base.html" %}

{% block title %}账号管理 - 视频同步系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users"></i> 账号管理</h2>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAccountModal">
        <i class="fas fa-plus"></i> 添加账号
    </button>
</div>

<!-- 账号统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="total-accounts-count">0</h4>
                        <p class="mb-0">总账号数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="active-accounts-count">0</h4>
                        <p class="mb-0">活跃账号</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="monitor-accounts-count">0</h4>
                        <p class="mb-0">监控账号</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-eye fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="upload-accounts-count">0</h4>
                        <p class="mb-0">上传账号</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-upload fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <input type="text" class="form-control" id="searchInput" placeholder="搜索账号名称...">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="platformFilter">
                    <option value="">所有平台</option>
                    <option value="xiaohongshu">小红书</option>
                    <option value="douyin">抖音</option>
                    <option value="tiktok">TikTok</option>
                    <option value="bilibili">B站</option>
                    <option value="youtube">YouTube</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="typeFilter">
                    <option value="">所有类型</option>
                    <option value="monitor">监控账号</option>
                    <option value="upload">上传账号</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i> 清空
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 账号列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">账号列表</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>账号名称</th>
                        <th>平台</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>最后使用</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="accounts-table-body">
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加账号模态框 -->
<div class="modal fade" id="addAccountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加账号</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addAccountForm" class="ajax-form" action="/api/accounts" method="POST" data-on-success="onAccountAdded">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountName" class="form-label">账号名称 *</label>
                                <input type="text" class="form-control" id="accountName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="platform" class="form-label">平台 *</label>
                                <select class="form-select" id="platform" name="platform" required>
                                    <option value="">请选择平台</option>
                                    <option value="xiaohongshu">小红书</option>
                                    <option value="douyin">抖音</option>
                                    <option value="tiktok">TikTok</option>
                                    <option value="bilibili">B站</option>
                                    <option value="youtube">YouTube</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountType" class="form-label">账号类型 *</label>
                                <select class="form-select" id="accountType" name="account_type" required>
                                    <option value="">请选择类型</option>
                                    <option value="monitor">监控账号</option>
                                    <option value="upload">上传账号</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="isActive" class="form-label">状态</label>
                                <select class="form-select" id="isActive" name="is_active">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cookies" class="form-label">Cookies *</label>
                        <textarea class="form-control" id="cookies" name="cookies" rows="4" 
                                  placeholder="请输入账号的Cookies信息..." required></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> 
                            请从浏览器开发者工具中复制完整的Cookies信息
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="config" class="form-label">配置信息 (JSON格式)</label>
                        <textarea class="form-control" id="config" name="config" rows="3" 
                                  placeholder='{"key": "value"}'></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> 
                            可选的JSON格式配置信息，如代理设置、请求头等
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存账号
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑账号模态框 -->
<div class="modal fade" id="editAccountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑账号</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editAccountForm">
                <div class="modal-body">
                    <input type="hidden" id="editAccountId">
                    <!-- 编辑表单内容与添加表单类似 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editAccountName" class="form-label">账号名称 *</label>
                                <input type="text" class="form-control" id="editAccountName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editIsActive" class="form-label">状态</label>
                                <select class="form-select" id="editIsActive">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editCookies" class="form-label">Cookies *</label>
                        <textarea class="form-control" id="editCookies" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editConfig" class="form-label">配置信息 (JSON格式)</label>
                        <textarea class="form-control" id="editConfig" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存更改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let accounts = [];
let filteredAccounts = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadAccounts();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 搜索输入
    document.getElementById('searchInput').addEventListener('input', debounce(filterAccounts, 300));
    
    // 筛选器
    document.getElementById('platformFilter').addEventListener('change', filterAccounts);
    document.getElementById('typeFilter').addEventListener('change', filterAccounts);
    
    // 表单验证
    document.getElementById('addAccountForm').addEventListener('submit', function(e) {
        if (!validateAccountForm(this)) {
            e.preventDefault();
        }
    });
}

// 加载账号列表
function loadAccounts() {
    fetch('/api/accounts')
        .then(response => response.json())
        .then(data => {
            accounts = data;
            filteredAccounts = [...accounts];
            updateAccountsTable();
            updateAccountStats();
        })
        .catch(error => {
            console.error('加载账号列表失败:', error);
            showMessage('加载账号列表失败', 'danger');
        });
}

// 更新账号表格
function updateAccountsTable() {
    const tbody = document.getElementById('accounts-table-body');
    
    if (filteredAccounts.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-inbox"></i> 暂无账号数据
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = filteredAccounts.map(account => `
        <tr>
            <td>
                <strong>${account.name}</strong>
            </td>
            <td>
                <span class="badge bg-${getPlatformColor(account.platform)}">
                    ${getPlatformName(account.platform)}
                </span>
            </td>
            <td>
                <span class="badge bg-${getTypeColor(account.account_type)}">
                    ${getTypeName(account.account_type)}
                </span>
            </td>
            <td>
                <span class="badge bg-${account.is_active ? 'success' : 'secondary'}">
                    ${account.is_active ? '启用' : '禁用'}
                </span>
            </td>
            <td>${formatDateTime(account.created_at)}</td>
            <td>${formatDateTime(account.last_used)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editAccount(${account.id})" 
                            data-bs-toggle="tooltip" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="testAccount(${account.id})" 
                            data-bs-toggle="tooltip" title="测试连接">
                        <i class="fas fa-plug"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteAccount(${account.id})" 
                            data-bs-toggle="tooltip" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // 重新初始化工具提示
    initTooltips();
}

// 更新账号统计
function updateAccountStats() {
    const totalCount = accounts.length;
    const activeCount = accounts.filter(a => a.is_active).length;
    const monitorCount = accounts.filter(a => a.account_type === 'monitor').length;
    const uploadCount = accounts.filter(a => a.account_type === 'upload').length;
    
    document.getElementById('total-accounts-count').textContent = totalCount;
    document.getElementById('active-accounts-count').textContent = activeCount;
    document.getElementById('monitor-accounts-count').textContent = monitorCount;
    document.getElementById('upload-accounts-count').textContent = uploadCount;
}

// 筛选账号
function filterAccounts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const platformFilter = document.getElementById('platformFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    
    filteredAccounts = accounts.filter(account => {
        const matchesSearch = account.name.toLowerCase().includes(searchTerm);
        const matchesPlatform = !platformFilter || account.platform === platformFilter;
        const matchesType = !typeFilter || account.account_type === typeFilter;
        
        return matchesSearch && matchesPlatform && matchesType;
    });
    
    updateAccountsTable();
}

// 清空筛选器
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('platformFilter').value = '';
    document.getElementById('typeFilter').value = '';
    filterAccounts();
}

// 验证账号表单
function validateAccountForm(form) {
    const configInput = form.querySelector('[name="config"]');
    if (configInput && configInput.value.trim()) {
        try {
            JSON.parse(configInput.value);
        } catch (e) {
            showMessage('配置信息必须是有效的JSON格式', 'danger');
            return false;
        }
    }
    return true;
}

// 账号添加成功回调
function onAccountAdded(data) {
    loadAccounts();
}

// 编辑账号
function editAccount(accountId) {
    const account = accounts.find(a => a.id === accountId);
    if (!account) return;
    
    // 填充编辑表单
    document.getElementById('editAccountId').value = account.id;
    document.getElementById('editAccountName').value = account.name;
    document.getElementById('editIsActive').value = account.is_active.toString();
    document.getElementById('editCookies').value = account.cookies;
    document.getElementById('editConfig').value = JSON.stringify(account.config || {}, null, 2);
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editAccountModal'));
    modal.show();
}

// 测试账号连接
function testAccount(accountId) {
    showMessage('测试连接功能开发中...', 'info');
}

// 删除账号
function deleteAccount(accountId) {
    const account = accounts.find(a => a.id === accountId);
    if (!account) return;
    
    showConfirmDialog(
        '确认删除账号',
        `确定要删除账号 "${account.name}" 吗？此操作不可撤销。`,
        function() {
            fetch(`/api/accounts/${accountId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'danger');
                } else {
                    showMessage('账号删除成功', 'success');
                    loadAccounts();
                }
            })
            .catch(error => {
                console.error('删除账号失败:', error);
                showMessage('删除账号失败', 'danger');
            });
        }
    );
}

// 工具函数
function getPlatformName(platform) {
    const names = {
        'xiaohongshu': '小红书',
        'douyin': '抖音',
        'tiktok': 'TikTok',
        'bilibili': 'B站',
        'youtube': 'YouTube'
    };
    return names[platform] || platform;
}

function getPlatformColor(platform) {
    const colors = {
        'xiaohongshu': 'danger',
        'douyin': 'dark',
        'tiktok': 'info',
        'bilibili': 'primary',
        'youtube': 'warning'
    };
    return colors[platform] || 'secondary';
}

function getTypeName(type) {
    const names = {
        'monitor': '监控账号',
        'upload': '上传账号'
    };
    return names[type] || type;
}

function getTypeColor(type) {
    const colors = {
        'monitor': 'info',
        'upload': 'warning'
    };
    return colors[type] || 'secondary';
}
</script>
{% endblock %}
