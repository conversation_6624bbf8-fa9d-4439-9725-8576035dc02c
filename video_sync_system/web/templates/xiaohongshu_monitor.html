{% extends "base.html" %}

{% block title %}小红书监控任务{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fab fa-instagram text-danger"></i> 小红书监控任务</h2>
                    <p class="text-muted mb-0">监控小红书用户最新视频作品，自动排除置顶和非视频内容</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="showCreateTaskModal()">
                        <i class="fas fa-plus"></i> 创建监控任务
                    </button>
                    <button class="btn btn-success ms-2" onclick="showTestModal()">
                        <i class="fas fa-vial"></i> 测试监控
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> 功能特点</h5>
                <ul class="mb-0">
                    <li><strong>智能过滤</strong>：自动排除置顶作品和非视频内容</li>
                    <li><strong>最新优先</strong>：优先获取用户最新发布的视频作品</li>
                    <li><strong>自动下载</strong>：使用spider_some_note进行高质量下载</li>
                    <li><strong>实时监控</strong>：定时检查用户新作品，支持自动上传到目标平台</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> 监控任务列表
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>任务名称</th>
                                    <th>监控用户</th>
                                    <th>状态</th>
                                    <th>最后运行</th>
                                    <th>成功/错误</th>
                                    <th>配置</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tasks-table">
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建任务模态框 -->
<div class="modal fade" id="createTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fab fa-instagram"></i> 创建小红书监控任务
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createTaskForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">任务名称 *</label>
                                <input type="text" class="form-control" name="name" required 
                                       placeholder="例如：监控用户A的视频">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">被监控用户ID *</label>
                                <input type="text" class="form-control" name="user_id" required
                                       placeholder="要监控的小红书用户ID">
                                <div class="form-text">要监控其作品的用户ID，从用户主页URL中获取，例如：565e638bcb35fb1b0c287ac7</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">监控账号 *</label>
                                <select class="form-select" name="monitor_account_id" required>
                                    <option value="">选择监控账号</option>
                                </select>
                                <div class="form-text">用于API访问的小红书账号（需要配置cookies）</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">目标上传账号</label>
                                <select class="form-select" name="target_account_id">
                                    <option value="">选择目标账号（可选）</option>
                                </select>
                                <div class="form-text">用于上传下载内容的目标平台账号（需要配置cookies）</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">检查间隔（秒）</label>
                                <input type="number" class="form-control" name="check_interval" 
                                       value="3600" min="300">
                                <div class="form-text">建议不少于5分钟（300秒）</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">下载数量限制</label>
                                <input type="number" class="form-control" name="download_limit" 
                                       value="10" min="1" max="50">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">自动上传</label>
                                <select class="form-select" name="auto_upload">
                                    <option value="true">是</option>
                                    <option value="false">否</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="exclude_pinned" 
                                           id="excludePinned" checked>
                                    <label class="form-check-label" for="excludePinned">
                                        排除置顶作品
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="video_only" 
                                           id="videoOnly" checked>
                                    <label class="form-check-label" for="videoOnly">
                                        只获取视频作品
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">任务描述</label>
                        <textarea class="form-control" name="description" rows="3" 
                                  placeholder="可选的任务描述"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createTask()">
                    <i class="fas fa-save"></i> 创建任务
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 测试监控模态框 -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-vial"></i> 测试小红书监控
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="testForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">被监控用户ID *</label>
                                <input type="text" class="form-control" name="user_id" required
                                       value="565e638bcb35fb1b0c287ac7"
                                       placeholder="要测试监控的用户ID">
                                <div class="form-text">要监控其作品的用户ID</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">监控账号 *</label>
                                <select class="form-select" name="account_id" required>
                                    <option value="">选择监控账号</option>
                                </select>
                                <div class="form-text">用于API访问的账号（需要cookies）</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">获取数量</label>
                                <input type="number" class="form-control" name="limit" 
                                       value="5" min="1" max="20">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="exclude_pinned" 
                                           id="testExcludePinned" checked>
                                    <label class="form-check-label" for="testExcludePinned">
                                        排除置顶
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="video_only" 
                                           id="testVideoOnly" checked>
                                    <label class="form-check-label" for="testVideoOnly">
                                        只要视频
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- 测试结果 -->
                <div id="testResults" class="mt-4" style="display: none;">
                    <h6><i class="fas fa-chart-bar"></i> 测试结果</h6>
                    <div id="testResultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" onclick="runTest()">
                    <i class="fas fa-play"></i> 开始测试
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadAccounts();
    loadTasks();
});

// 加载账号列表
function loadAccounts() {
    fetch('/api/accounts')
        .then(response => response.json())
        .then(accounts => {
            const monitorSelects = document.querySelectorAll('select[name="monitor_account_id"], select[name="account_id"]');
            const targetSelects = document.querySelectorAll('select[name="target_account_id"]');
            
            // 填充监控账号选项（小红书账号）
            monitorSelects.forEach(select => {
                select.innerHTML = '<option value="">选择监控账号</option>';
                accounts.filter(acc => acc.platform === 'xiaohongshu' && acc.account_type === 'monitor')
                    .forEach(account => {
                        select.innerHTML += `<option value="${account.id}">${account.name}</option>`;
                    });
            });
            
            // 填充目标账号选项（上传账号）
            targetSelects.forEach(select => {
                select.innerHTML = '<option value="">选择目标账号（可选）</option>';
                accounts.filter(acc => acc.account_type === 'upload')
                    .forEach(account => {
                        select.innerHTML += `<option value="${account.id}">${account.name} (${account.platform})</option>`;
                    });
            });
        })
        .catch(error => {
            console.error('加载账号失败:', error);
            showAlert('加载账号失败', 'danger');
        });
}

// 加载任务列表
function loadTasks() {
    fetch('/api/tasks')
        .then(response => response.json())
        .then(tasks => {
            updateTasksTable(tasks.filter(task => task.source_platform === 'xiaohongshu'));
        })
        .catch(error => {
            console.error('加载任务失败:', error);
            document.getElementById('tasks-table').innerHTML = 
                '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
        });
}

// 更新任务表格
function updateTasksTable(tasks) {
    const tbody = document.getElementById('tasks-table');
    
    if (tasks.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无小红书监控任务</td></tr>';
        return;
    }
    
    tbody.innerHTML = tasks.map(task => `
        <tr>
            <td>${task.name}</td>
            <td>${task.source_user_id || '--'}</td>
            <td><span class="badge bg-${getStatusColor(task.status)}">${getStatusText(task.status)}</span></td>
            <td>${task.last_run ? new Date(task.last_run).toLocaleString() : '--'}</td>
            <td>${task.success_count || 0} / ${task.error_count || 0}</td>
            <td>
                <small>
                    ${task.config?.exclude_pinned ? '<i class="fas fa-check text-success" title="排除置顶"></i>' : '<i class="fas fa-times text-muted" title="包含置顶"></i>'}
                    ${task.config?.video_only ? '<i class="fas fa-video text-primary ms-1" title="只要视频"></i>' : '<i class="fas fa-images text-muted ms-1" title="包含图片"></i>'}
                </small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="startTask('${task.id}')" 
                            ${task.status === 'running' ? 'disabled' : ''}>
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="stopTask('${task.id}')"
                            ${task.status !== 'running' ? 'disabled' : ''}>
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteTask('${task.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 显示创建任务模态框
function showCreateTaskModal() {
    new bootstrap.Modal(document.getElementById('createTaskModal')).show();
}

// 显示测试模态框
function showTestModal() {
    new bootstrap.Modal(document.getElementById('testModal')).show();
}

// 创建任务
function createTask() {
    const form = document.getElementById('createTaskForm');
    const formData = new FormData(form);
    
    const data = {
        name: formData.get('name'),
        user_id: formData.get('user_id'),
        monitor_account_id: formData.get('monitor_account_id'),
        target_account_id: formData.get('target_account_id'),
        check_interval: parseInt(formData.get('check_interval')),
        download_limit: parseInt(formData.get('download_limit')),
        auto_upload: formData.get('auto_upload') === 'true',
        exclude_pinned: formData.has('exclude_pinned'),
        video_only: formData.has('video_only'),
        description: formData.get('description')
    };
    
    fetch('/api/tasks/xiaohongshu/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('任务创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('createTaskModal')).hide();
            form.reset();
            loadTasks();
        } else {
            showAlert(result.error || '任务创建失败', 'danger');
        }
    })
    .catch(error => {
        console.error('创建任务失败:', error);
        showAlert('创建任务失败', 'danger');
    });
}

// 运行测试
function runTest() {
    const form = document.getElementById('testForm');
    const formData = new FormData(form);
    
    const data = {
        user_id: formData.get('user_id'),
        account_id: formData.get('account_id'),
        limit: parseInt(formData.get('limit')),
        exclude_pinned: formData.has('exclude_pinned'),
        video_only: formData.has('video_only')
    };
    
    const resultsDiv = document.getElementById('testResults');
    const contentDiv = document.getElementById('testResultsContent');
    
    contentDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 测试中...</div>';
    resultsDiv.style.display = 'block';
    
    fetch('/api/tasks/xiaohongshu/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            let html = `
                <div class="alert alert-success">
                    <strong>测试成功！</strong> 用户 ${result.user_id} 共找到 ${result.total_found} 条符合条件的视频作品
                </div>
            `;
            
            if (result.content_items.length > 0) {
                html += '<div class="table-responsive"><table class="table table-sm">';
                html += '<thead><tr><th>标题</th><th>类型</th><th>置顶</th><th>链接</th></tr></thead><tbody>';
                
                result.content_items.forEach(item => {
                    html += `
                        <tr>
                            <td>${item.title || '无标题'}</td>
                            <td><span class="badge bg-${item.content_type === 'video' ? 'primary' : 'secondary'}">${item.content_type}</span></td>
                            <td>${item.is_pinned ? '<i class="fas fa-thumbtack text-warning"></i>' : '--'}</td>
                            <td><a href="${item.url}" target="_blank" class="btn btn-sm btn-outline-primary">查看</a></td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
            }
            
            contentDiv.innerHTML = html;
        } else {
            contentDiv.innerHTML = `<div class="alert alert-danger">测试失败：${result.error}</div>`;
        }
    })
    .catch(error => {
        console.error('测试失败:', error);
        contentDiv.innerHTML = '<div class="alert alert-danger">测试失败，请检查网络连接</div>';
    });
}

// 启动任务
function startTask(taskId) {
    fetch(`/api/tasks/${taskId}/start`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('任务启动成功', 'success');
            loadTasks();
        } else {
            showAlert(result.error || '任务启动失败', 'danger');
        }
    })
    .catch(error => {
        console.error('启动任务失败:', error);
        showAlert('启动任务失败', 'danger');
    });
}

// 停止任务
function stopTask(taskId) {
    fetch(`/api/tasks/${taskId}/stop`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('任务停止成功', 'success');
            loadTasks();
        } else {
            showAlert(result.error || '任务停止失败', 'danger');
        }
    })
    .catch(error => {
        console.error('停止任务失败:', error);
        showAlert('停止任务失败', 'danger');
    });
}

// 删除任务
function deleteTask(taskId) {
    if (!confirm('确定要删除这个任务吗？')) {
        return;
    }
    
    fetch(`/api/tasks/${taskId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('任务删除成功', 'success');
            loadTasks();
        } else {
            showAlert(result.error || '任务删除失败', 'danger');
        }
    })
    .catch(error => {
        console.error('删除任务失败:', error);
        showAlert('删除任务失败', 'danger');
    });
}

// 工具函数
function getStatusColor(status) {
    const colors = {
        'running': 'success',
        'paused': 'warning',
        'completed': 'info',
        'failed': 'danger',
        'created': 'secondary'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'running': '运行中',
        'paused': '已暂停',
        'completed': '已完成',
        'failed': '失败',
        'created': '已创建'
    };
    return texts[status] || status;
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
{% endblock %}
