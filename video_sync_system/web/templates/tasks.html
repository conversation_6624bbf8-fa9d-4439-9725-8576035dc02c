{% extends "base.html" %}

{% block title %}任务管理 - 视频同步系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tasks"></i> 任务管理</h2>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTaskModal">
        <i class="fas fa-plus"></i> 创建任务
    </button>
</div>

<!-- 任务统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="total-tasks-count">0</h4>
                        <p class="mb-0">总任务数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tasks fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="running-tasks-count">0</h4>
                        <p class="mb-0">运行中</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-play fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="paused-tasks-count">0</h4>
                        <p class="mb-0">已暂停</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pause fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="failed-tasks-count">0</h4>
                        <p class="mb-0">失败</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <input type="text" class="form-control" id="searchInput" placeholder="搜索任务名称...">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">所有状态</option>
                    <option value="running">运行中</option>
                    <option value="paused">已暂停</option>
                    <option value="completed">已完成</option>
                    <option value="failed">失败</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="platformFilter">
                    <option value="">所有平台</option>
                    <option value="xiaohongshu">小红书</option>
                    <option value="douyin">抖音</option>
                    <option value="tiktok">TikTok</option>
                    <option value="bilibili">B站</option>
                    <option value="youtube">YouTube</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i> 清空
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">任务列表</h5>
        <div>
            <button class="btn btn-sm btn-outline-success" onclick="startAllTasks()">
                <i class="fas fa-play"></i> 全部启动
            </button>
            <button class="btn btn-sm btn-outline-warning" onclick="pauseAllTasks()">
                <i class="fas fa-pause"></i> 全部暂停
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>源平台</th>
                        <th>目标平台</th>
                        <th>状态</th>
                        <th>成功/失败</th>
                        <th>最后运行</th>
                        <th>下次运行</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="tasks-table-body">
                    <tr>
                        <td colspan="8" class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 创建任务模态框 -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建同步任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addTaskForm" class="ajax-form" action="/api/tasks" method="POST" data-on-success="onTaskAdded">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskName" class="form-label">任务名称 *</label>
                                <input type="text" class="form-control" id="taskName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sourcePlatform" class="form-label">源平台 *</label>
                                <select class="form-select" id="sourcePlatform" name="source_platform" required>
                                    <option value="">请选择源平台</option>
                                    <option value="xiaohongshu">小红书</option>
                                    <option value="douyin">抖音</option>
                                    <option value="tiktok">TikTok</option>
                                    <option value="bilibili">B站</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sourceUserId" class="form-label">源用户ID *</label>
                                <input type="text" class="form-control" id="sourceUserId" name="source_user_id" 
                                       placeholder="要监控的用户ID或用户名" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="monitorAccount" class="form-label">监控账号 *</label>
                                <select class="form-select" id="monitorAccount" name="monitor_account_id" required>
                                    <option value="">请选择监控账号</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">目标平台 *</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="targetDouyin" name="target_platforms" value="douyin">
                                    <label class="form-check-label" for="targetDouyin">抖音</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="targetTiktok" name="target_platforms" value="tiktok">
                                    <label class="form-check-label" for="targetTiktok">TikTok</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="targetXiaohongshu" name="target_platforms" value="xiaohongshu">
                                    <label class="form-check-label" for="targetXiaohongshu">小红书</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="targetBilibili" name="target_platforms" value="bilibili">
                                    <label class="form-check-label" for="targetBilibili">B站</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="targetYoutube" name="target_platforms" value="youtube">
                                    <label class="form-check-label" for="targetYoutube">YouTube</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="uploadAccountsSection">
                        <!-- 上传账号选择将通过JavaScript动态生成 -->
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="checkInterval" class="form-label">检查间隔 (秒)</label>
                                <input type="number" class="form-control" id="checkInterval" name="check_interval" 
                                       value="3600" min="60">
                                <div class="form-text">建议不少于60秒</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="downloadLimit" class="form-label">下载限制</label>
                                <input type="number" class="form-control" id="downloadLimit" name="download_limit" 
                                       value="10" min="1">
                                <div class="form-text">每次检查最多下载的视频数</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="autoStart" class="form-label">自动启动</label>
                                <select class="form-select" id="autoStart" name="auto_start">
                                    <option value="true">是</option>
                                    <option value="false">否</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 创建任务
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任务详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="taskDetailContent">
                    <!-- 任务详情内容将通过JavaScript填充 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let tasks = [];
let filteredTasks = [];
let accounts = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadTasks();
    loadAccounts();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 搜索输入
    document.getElementById('searchInput').addEventListener('input', debounce(filterTasks, 300));

    // 筛选器
    document.getElementById('statusFilter').addEventListener('change', filterTasks);
    document.getElementById('platformFilter').addEventListener('change', filterTasks);

    // 目标平台选择变化时更新上传账号选择
    document.querySelectorAll('input[name="target_platforms"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateUploadAccountsSection);
    });

    // 源平台选择变化时更新监控账号选择
    document.getElementById('sourcePlatform').addEventListener('change', updateMonitorAccountsSelect);
}

// 加载任务列表
function loadTasks() {
    fetch('/api/tasks')
        .then(response => response.json())
        .then(data => {
            tasks = data;
            filteredTasks = [...tasks];
            updateTasksTable();
            updateTaskStats();
        })
        .catch(error => {
            console.error('加载任务列表失败:', error);
            showMessage('加载任务列表失败', 'danger');
        });
}

// 加载账号列表
function loadAccounts() {
    fetch('/api/accounts')
        .then(response => response.json())
        .then(data => {
            accounts = data;
            updateMonitorAccountsSelect();
            updateUploadAccountsSection();
        })
        .catch(error => {
            console.error('加载账号列表失败:', error);
        });
}

// 更新任务表格
function updateTasksTable() {
    const tbody = document.getElementById('tasks-table-body');

    if (filteredTasks.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-inbox"></i> 暂无任务数据
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = filteredTasks.map(task => `
        <tr>
            <td>
                <strong>${task.name}</strong>
                <br>
                <small class="text-muted">${task.source_user_id}</small>
            </td>
            <td>
                <span class="badge bg-${getPlatformColor(task.source_platform)}">
                    ${getPlatformName(task.source_platform)}
                </span>
            </td>
            <td>
                ${task.target_platforms.map(platform =>
                    `<span class="badge bg-${getPlatformColor(platform)} me-1">${getPlatformName(platform)}</span>`
                ).join('')}
            </td>
            <td>
                <span class="badge bg-${getStatusColor(task.status)}">
                    <i class="status-indicator status-${task.status}"></i>
                    ${getStatusText(task.status)}
                </span>
            </td>
            <td>
                <span class="text-success">${task.success_count || 0}</span> /
                <span class="text-danger">${task.error_count || 0}</span>
            </td>
            <td>${formatDateTime(task.last_run)}</td>
            <td>${formatDateTime(task.next_run)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    ${task.status === 'running' ?
                        `<button class="btn btn-outline-warning" onclick="pauseTask('${task.id}')"
                                data-bs-toggle="tooltip" title="暂停">
                            <i class="fas fa-pause"></i>
                        </button>` :
                        `<button class="btn btn-outline-success" onclick="startTask('${task.id}')"
                                data-bs-toggle="tooltip" title="启动">
                            <i class="fas fa-play"></i>
                        </button>`
                    }
                    <button class="btn btn-outline-info" onclick="viewTaskDetail('${task.id}')"
                            data-bs-toggle="tooltip" title="详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-primary" onclick="editTask('${task.id}')"
                            data-bs-toggle="tooltip" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteTask('${task.id}')"
                            data-bs-toggle="tooltip" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    // 重新初始化工具提示
    initTooltips();
}

// 更新任务统计
function updateTaskStats() {
    const totalCount = tasks.length;
    const runningCount = tasks.filter(t => t.status === 'running').length;
    const pausedCount = tasks.filter(t => t.status === 'paused').length;
    const failedCount = tasks.filter(t => t.status === 'failed').length;

    document.getElementById('total-tasks-count').textContent = totalCount;
    document.getElementById('running-tasks-count').textContent = runningCount;
    document.getElementById('paused-tasks-count').textContent = pausedCount;
    document.getElementById('failed-tasks-count').textContent = failedCount;
}

// 筛选任务
function filterTasks() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const platformFilter = document.getElementById('platformFilter').value;

    filteredTasks = tasks.filter(task => {
        const matchesSearch = task.name.toLowerCase().includes(searchTerm) ||
                            task.source_user_id.toLowerCase().includes(searchTerm);
        const matchesStatus = !statusFilter || task.status === statusFilter;
        const matchesPlatform = !platformFilter ||
                              task.source_platform === platformFilter ||
                              task.target_platforms.includes(platformFilter);

        return matchesSearch && matchesStatus && matchesPlatform;
    });

    updateTasksTable();
}

// 清空筛选器
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('platformFilter').value = '';
    filterTasks();
}

// 更新监控账号选择
function updateMonitorAccountsSelect() {
    const sourcePlatform = document.getElementById('sourcePlatform').value;
    const monitorSelect = document.getElementById('monitorAccount');

    // 清空现有选项
    monitorSelect.innerHTML = '<option value="">请选择监控账号</option>';

    if (!sourcePlatform) return;

    // 筛选对应平台的监控账号
    const monitorAccounts = accounts.filter(account =>
        account.platform === sourcePlatform &&
        account.account_type === 'monitor' &&
        account.is_active
    );

    monitorAccounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.id;
        option.textContent = account.name;
        monitorSelect.appendChild(option);
    });
}

// 更新上传账号选择区域
function updateUploadAccountsSection() {
    const selectedPlatforms = Array.from(document.querySelectorAll('input[name="target_platforms"]:checked'))
                                  .map(cb => cb.value);
    const section = document.getElementById('uploadAccountsSection');

    if (selectedPlatforms.length === 0) {
        section.innerHTML = '';
        return;
    }

    let html = '<div class="mb-3"><label class="form-label">上传账号 *</label>';

    selectedPlatforms.forEach(platform => {
        const uploadAccounts = accounts.filter(account =>
            account.platform === platform &&
            account.account_type === 'upload' &&
            account.is_active
        );

        html += `
            <div class="mb-2">
                <label class="form-label">${getPlatformName(platform)}</label>
                <select class="form-select" name="upload_account_${platform}" required>
                    <option value="">请选择${getPlatformName(platform)}上传账号</option>
                    ${uploadAccounts.map(account =>
                        `<option value="${account.id}">${account.name}</option>`
                    ).join('')}
                </select>
            </div>
        `;
    });

    html += '</div>';
    section.innerHTML = html;
}

// 任务操作函数
function startTask(taskId) {
    fetch(`/api/tasks/${taskId}/start`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage(data.error, 'danger');
        } else {
            showMessage('任务启动成功', 'success');
            loadTasks();
        }
    })
    .catch(error => {
        console.error('启动任务失败:', error);
        showMessage('启动任务失败', 'danger');
    });
}

function pauseTask(taskId) {
    fetch(`/api/tasks/${taskId}/stop`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage(data.error, 'danger');
        } else {
            showMessage('任务已暂停', 'success');
            loadTasks();
        }
    })
    .catch(error => {
        console.error('暂停任务失败:', error);
        showMessage('暂停任务失败', 'danger');
    });
}

function deleteTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    showConfirmDialog(
        '确认删除任务',
        `确定要删除任务 "${task.name}" 吗？此操作不可撤销。`,
        function() {
            fetch(`/api/tasks/${taskId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'danger');
                } else {
                    showMessage('任务删除成功', 'success');
                    loadTasks();
                }
            })
            .catch(error => {
                console.error('删除任务失败:', error);
                showMessage('删除任务失败', 'danger');
            });
        }
    );
}

function viewTaskDetail(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    const detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>任务名称:</td><td>${task.name}</td></tr>
                    <tr><td>源平台:</td><td>${getPlatformName(task.source_platform)}</td></tr>
                    <tr><td>源用户:</td><td>${task.source_user_id}</td></tr>
                    <tr><td>状态:</td><td><span class="badge bg-${getStatusColor(task.status)}">${getStatusText(task.status)}</span></td></tr>
                    <tr><td>检查间隔:</td><td>${task.check_interval}秒</td></tr>
                    <tr><td>下载限制:</td><td>${task.download_limit}个</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>运行统计</h6>
                <table class="table table-sm">
                    <tr><td>成功次数:</td><td class="text-success">${task.success_count || 0}</td></tr>
                    <tr><td>失败次数:</td><td class="text-danger">${task.error_count || 0}</td></tr>
                    <tr><td>创建时间:</td><td>${formatDateTime(task.created_at)}</td></tr>
                    <tr><td>最后运行:</td><td>${formatDateTime(task.last_run)}</td></tr>
                    <tr><td>下次运行:</td><td>${formatDateTime(task.next_run)}</td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>目标平台</h6>
                <div>
                    ${task.target_platforms.map(platform =>
                        `<span class="badge bg-${getPlatformColor(platform)} me-2">${getPlatformName(platform)}</span>`
                    ).join('')}
                </div>
            </div>
        </div>
    `;

    document.getElementById('taskDetailContent').innerHTML = detailHtml;
    const modal = new bootstrap.Modal(document.getElementById('taskDetailModal'));
    modal.show();
}

function editTask(taskId) {
    showMessage('编辑任务功能开发中...', 'info');
}

function startAllTasks() {
    showConfirmDialog(
        '确认启动所有任务',
        '确定要启动所有已暂停的任务吗？',
        function() {
            const pausedTasks = tasks.filter(t => t.status === 'paused');
            let completed = 0;

            pausedTasks.forEach(task => {
                startTask(task.id);
                completed++;
                if (completed === pausedTasks.length) {
                    setTimeout(() => loadTasks(), 1000);
                }
            });

            if (pausedTasks.length === 0) {
                showMessage('没有需要启动的任务', 'info');
            }
        }
    );
}

function pauseAllTasks() {
    showConfirmDialog(
        '确认暂停所有任务',
        '确定要暂停所有运行中的任务吗？',
        function() {
            const runningTasks = tasks.filter(t => t.status === 'running');
            let completed = 0;

            runningTasks.forEach(task => {
                pauseTask(task.id);
                completed++;
                if (completed === runningTasks.length) {
                    setTimeout(() => loadTasks(), 1000);
                }
            });

            if (runningTasks.length === 0) {
                showMessage('没有需要暂停的任务', 'info');
            }
        }
    );
}

// 任务创建成功回调
function onTaskAdded(data) {
    loadTasks();
}

// 工具函数
function getPlatformName(platform) {
    const names = {
        'xiaohongshu': '小红书',
        'douyin': '抖音',
        'tiktok': 'TikTok',
        'bilibili': 'B站',
        'youtube': 'YouTube'
    };
    return names[platform] || platform;
}

function getPlatformColor(platform) {
    const colors = {
        'xiaohongshu': 'danger',
        'douyin': 'dark',
        'tiktok': 'info',
        'bilibili': 'primary',
        'youtube': 'warning'
    };
    return colors[platform] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'running': 'success',
        'paused': 'warning',
        'completed': 'info',
        'failed': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'running': '运行中',
        'paused': '已暂停',
        'completed': '已完成',
        'failed': '失败'
    };
    return texts[status] || status;
}
</script>
{% endblock %}
