/* 自定义样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}

/* 状态徽章样式 */
.badge {
    font-size: 0.8em;
    padding: 0.4em 0.6em;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 10px 10px;
}

/* 日志容器样式 */
#log-container {
    font-family: 'Courier New', monospace;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

#log-container .log-entry {
    margin-bottom: 2px;
    padding: 2px 0;
}

#log-container .log-info {
    color: #17a2b8;
}

#log-container .log-warning {
    color: #ffc107;
}

#log-container .log-error {
    color: #dc3545;
}

#log-container .log-debug {
    color: #6c757d;
}

/* 消息提示样式 */
.alert {
    border: none;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-running {
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.status-stopped {
    background-color: #dc3545;
}

.status-paused {
    background-color: #ffc107;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm > .btn, .btn-sm {
        padding: 0.2rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

/* 进度条 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* 列表组 */
.list-group-item {
    border: 1px solid #e9ecef;
    border-radius: 6px !important;
    margin-bottom: 0.5rem;
}

.list-group-item:last-child {
    margin-bottom: 0;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 特殊样式 */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .card {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .card-header {
        background-color: #2d3748;
        border-bottom-color: #4a5568;
    }
    
    .table {
        color: #e9ecef;
    }
    
    .table th {
        background-color: #2d3748;
        color: #e9ecef;
    }
}
