// 主应用JavaScript文件

// 全局变量
let socket;
let messageTimeout;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 初始化WebSocket连接
    initWebSocket();
    
    // 设置全局事件监听器
    setupGlobalEventListeners();
    
    // 初始化工具提示
    initTooltips();
    
    console.log('应用初始化完成');
}

// 初始化WebSocket连接
function initWebSocket() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('WebSocket连接成功');
        showMessage('已连接到服务器', 'success', 3000);
    });
    
    socket.on('disconnect', function() {
        console.log('WebSocket连接断开');
        showMessage('与服务器连接断开', 'warning');
    });
    
    socket.on('reconnect', function() {
        console.log('WebSocket重新连接');
        showMessage('已重新连接到服务器', 'success', 3000);
    });
    
    // 监听系统事件
    socket.on('account_created', function(data) {
        showMessage(`账号 "${data.name}" 创建成功`, 'success');
        refreshCurrentPage();
    });
    
    socket.on('account_deleted', function(data) {
        showMessage('账号删除成功', 'success');
        refreshCurrentPage();
    });
    
    socket.on('task_created', function(data) {
        showMessage(`任务 "${data.name}" 创建成功`, 'success');
        refreshCurrentPage();
    });
    
    socket.on('task_started', function(data) {
        showMessage('任务启动成功', 'success');
        refreshCurrentPage();
    });
    
    socket.on('task_stopped', function(data) {
        showMessage('任务停止成功', 'info');
        refreshCurrentPage();
    });
    
    socket.on('task_deleted', function(data) {
        showMessage('任务删除成功', 'success');
        refreshCurrentPage();
    });
    
    // 监听日志事件
    socket.on('log_message', function(data) {
        appendLogMessage(data);
    });
}

// 设置全局事件监听器
function setupGlobalEventListeners() {
    // 导航链接高亮
    highlightActiveNavLink();
    
    // 表单提交处理
    document.addEventListener('submit', function(e) {
        const form = e.target;
        if (form.classList.contains('ajax-form')) {
            e.preventDefault();
            handleAjaxForm(form);
        }
    });
    
    // 确认删除对话框
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('confirm-delete')) {
            e.preventDefault();
            showConfirmDialog(
                '确认删除',
                '此操作不可撤销，确定要删除吗？',
                function() {
                    window.location.href = e.target.href;
                }
            );
        }
    });
}

// 初始化工具提示
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 高亮当前页面的导航链接
function highlightActiveNavLink() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// 显示消息提示
function showMessage(message, type = 'info', duration = 5000) {
    const container = document.getElementById('message-container');
    if (!container) return;
    
    // 清除之前的定时器
    if (messageTimeout) {
        clearTimeout(messageTimeout);
    }
    
    // 创建消息元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 添加到容器
    container.innerHTML = '';
    container.appendChild(alertDiv);
    
    // 自动隐藏
    if (duration > 0) {
        messageTimeout = setTimeout(() => {
            const alert = bootstrap.Alert.getOrCreateInstance(alertDiv);
            alert.close();
        }, duration);
    }
}

// 显示确认对话框
function showConfirmDialog(title, message, onConfirm, onCancel) {
    const modalHtml = `
        <div class="modal fade" id="confirmModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-danger" id="confirmBtn">确认</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除已存在的模态框
    const existingModal = document.getElementById('confirmModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    
    // 绑定确认按钮事件
    document.getElementById('confirmBtn').addEventListener('click', function() {
        modal.hide();
        if (onConfirm) onConfirm();
    });
    
    // 绑定取消事件
    document.getElementById('confirmModal').addEventListener('hidden.bs.modal', function() {
        if (onCancel) onCancel();
        this.remove();
    });
    
    modal.show();
}

// 处理AJAX表单提交
function handleAjaxForm(form) {
    const formData = new FormData(form);
    const url = form.action || window.location.pathname;
    const method = form.method || 'POST';
    
    // 显示加载状态
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
    submitBtn.disabled = true;
    
    fetch(url, {
        method: method,
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage(data.error, 'danger');
        } else {
            showMessage(data.message || '操作成功', 'success');
            
            // 如果有回调函数，执行它
            if (form.dataset.onSuccess) {
                const callback = window[form.dataset.onSuccess];
                if (typeof callback === 'function') {
                    callback(data);
                }
            }
            
            // 重置表单
            form.reset();
            
            // 关闭模态框（如果存在）
            const modal = form.closest('.modal');
            if (modal) {
                bootstrap.Modal.getInstance(modal).hide();
            }
        }
    })
    .catch(error => {
        console.error('表单提交错误:', error);
        showMessage('操作失败，请重试', 'danger');
    })
    .finally(() => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// 刷新当前页面数据
function refreshCurrentPage() {
    // 根据当前页面调用相应的刷新函数
    const path = window.location.pathname;
    
    if (path === '/' && typeof loadDashboardData === 'function') {
        loadDashboardData();
    } else if (path === '/accounts' && typeof loadAccounts === 'function') {
        loadAccounts();
    } else if (path === '/tasks' && typeof loadTasks === 'function') {
        loadTasks();
    }
}

// 添加日志消息
function appendLogMessage(data) {
    const logContainer = document.getElementById('log-container');
    if (!logContainer) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${data.level.toLowerCase()}`;
    logEntry.textContent = `[${timestamp}] ${data.level}: ${data.message}`;
    
    logContainer.appendChild(logEntry);
    
    // 自动滚动到底部（如果启用）
    if (window.autoScroll !== false) {
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    // 限制日志条数，避免内存占用过多
    const maxLogEntries = 1000;
    const logEntries = logContainer.querySelectorAll('.log-entry');
    if (logEntries.length > maxLogEntries) {
        for (let i = 0; i < logEntries.length - maxLogEntries; i++) {
            logEntries[i].remove();
        }
    }
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '--';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showMessage('已复制到剪贴板', 'success', 2000);
        });
    } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showMessage('已复制到剪贴板', 'success', 2000);
    }
}

// 导出全局函数
window.showMessage = showMessage;
window.showConfirmDialog = showConfirmDialog;
window.formatDateTime = formatDateTime;
window.formatFileSize = formatFileSize;
window.copyToClipboard = copyToClipboard;
