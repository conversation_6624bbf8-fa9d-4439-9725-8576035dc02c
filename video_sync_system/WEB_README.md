# 视频同步系统 Web 管理界面

## 概述

本系统提供了一个现代化的Web管理界面，用于管理视频监控和自动上传任务。界面采用Bootstrap 5设计，支持响应式布局，提供直观的操作体验。

## 功能特性

### 🏠 仪表板
- 系统运行状态概览
- 任务执行统计
- 最近活动记录
- 系统资源监控

### 👥 账号管理
- 添加/编辑/删除平台账号
- 支持监控账号和上传账号
- 账号状态管理
- Cookies配置管理
- 账号连接测试

### 📋 任务管理
- 创建视频同步任务
- 任务状态监控（运行中/暂停/失败）
- 批量任务操作
- 任务执行统计
- 任务详情查看

### 📊 日志查看
- 实时日志显示
- 日志级别筛选
- 历史日志查询
- 日志导出功能
- WebSocket实时更新

## 快速开始

### 1. 安装依赖

```bash
cd video_sync_system
pip install -r requirements.txt
```

### 2. 初始化数据库

```bash
python run_web.py --init-db
```

### 3. 启动Web服务器

```bash
# 开发模式
python run_web.py --debug

# 生产模式
python run_web.py --host 0.0.0.0 --port 8080
```

### 4. 访问Web界面

打开浏览器访问：http://localhost:5000

## 使用指南

### 账号配置

1. **添加监控账号**
   - 进入"账号管理"页面
   - 点击"添加账号"
   - 选择平台和账号类型（监控账号）
   - 输入账号名称和Cookies
   - 保存配置

2. **添加上传账号**
   - 同样在"账号管理"页面添加
   - 选择账号类型为"上传账号"
   - 配置对应平台的上传凭据

### 任务创建

1. **创建同步任务**
   - 进入"任务管理"页面
   - 点击"创建任务"
   - 配置源平台和目标用户
   - 选择监控账号
   - 选择目标平台和上传账号
   - 设置检查间隔和其他参数
   - 保存并启动任务

2. **任务监控**
   - 在任务列表中查看任务状态
   - 点击"详情"查看任务执行情况
   - 使用"启动/暂停"控制任务运行

### 日志监控

1. **实时日志**
   - 进入"日志查看"页面
   - 实时查看系统运行日志
   - 使用筛选器查找特定日志

2. **历史查询**
   - 点击"历史日志查询"
   - 选择时间范围
   - 导出查询结果

## API接口

### 账号管理 API

```
GET    /api/accounts          # 获取账号列表
POST   /api/accounts          # 创建账号
PUT    /api/accounts/{id}     # 更新账号
DELETE /api/accounts/{id}     # 删除账号
```

### 任务管理 API

```
GET    /api/tasks             # 获取任务列表
POST   /api/tasks             # 创建任务
PUT    /api/tasks/{id}        # 更新任务
DELETE /api/tasks/{id}        # 删除任务
POST   /api/tasks/{id}/start  # 启动任务
POST   /api/tasks/{id}/stop   # 停止任务
```

### 系统状态 API

```
GET    /api/status            # 获取系统状态
GET    /api/stats             # 获取统计信息
GET    /api/logs              # 获取日志
```

## 配置说明

### 环境变量

```bash
# Flask配置
FLASK_ENV=development          # 开发/生产环境
FLASK_DEBUG=1                  # 调试模式

# 数据库配置
DATABASE_URL=sqlite:///data/video_sync.db

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/web_server.log
```

### 配置文件

系统配置文件位于 `config/config.yaml`，包含：

- 数据库连接配置
- 日志配置
- 平台API配置
- 任务调度配置

## 安全注意事项

1. **生产环境部署**
   - 不要在生产环境使用调试模式
   - 配置适当的防火墙规则
   - 使用HTTPS协议
   - 定期备份数据库

2. **账号安全**
   - Cookies信息加密存储
   - 定期更新账号凭据
   - 监控异常登录活动

3. **访问控制**
   - 建议添加用户认证
   - 限制管理界面访问IP
   - 使用强密码策略

## 故障排除

### 常见问题

1. **无法启动服务器**
   - 检查端口是否被占用
   - 确认依赖包已正确安装
   - 查看错误日志

2. **数据库连接失败**
   - 确认数据库文件权限
   - 检查数据库路径配置
   - 重新初始化数据库

3. **任务无法启动**
   - 检查账号配置是否正确
   - 确认网络连接正常
   - 查看任务日志详情

### 日志位置

- Web服务器日志：`logs/web_server.log`
- 任务执行日志：`logs/task_manager.log`
- 系统错误日志：`logs/error.log`

## 开发说明

### 项目结构

```
web/
├── app.py              # Flask应用主文件
├── routes/             # 路由定义
│   ├── __init__.py
│   ├── main.py         # 主页面路由
│   ├── api.py          # API路由
│   └── websocket.py    # WebSocket处理
├── templates/          # HTML模板
│   ├── base.html       # 基础模板
│   ├── index.html      # 仪表板
│   ├── accounts.html   # 账号管理
│   ├── tasks.html      # 任务管理
│   └── logs.html       # 日志查看
└── static/             # 静态资源
    ├── css/            # 样式文件
    ├── js/             # JavaScript文件
    └── img/            # 图片资源
```

### 扩展开发

1. **添加新页面**
   - 在 `templates/` 中创建HTML模板
   - 在 `routes/` 中添加路由处理
   - 更新导航菜单

2. **添加新API**
   - 在 `routes/api.py` 中添加端点
   - 实现相应的业务逻辑
   - 更新前端JavaScript调用

3. **自定义样式**
   - 修改 `static/css/custom.css`
   - 使用Bootstrap变量自定义主题
   - 添加响应式设计

## 技术栈

- **后端**: Flask + Flask-SocketIO
- **前端**: Bootstrap 5 + jQuery + Font Awesome
- **数据库**: SQLite
- **实时通信**: WebSocket
- **任务调度**: APScheduler

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
