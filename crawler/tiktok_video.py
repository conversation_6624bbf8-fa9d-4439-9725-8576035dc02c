import asyncio
from pathlib import Path
import traceback

from f2.apps.tiktok.handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from f2.apps.tiktok.db import AsyncUserDB
from f2.apps.tiktok.dl import TiktokDownloader
from f2.utils.conf_manager import ConfigManager
from f2.cli.cli_console import RichConsoleManager
from f2.log.logger import logger
from f2.apps.tiktok.utils import SecUserIdFetcher
from f2.utils.utils import extract_valid_urls
from f2.apps.douyin.handler import DouyinHandler
from f2.apps.douyin.db import AsyncUserDB
from f2.apps.douyin.dl import DouyinDownloader

# 全局配置参数，保护敏感信息
kwargs = {
    "headers": {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0",
        "Referer": "https://www.tiktok.com/",
    },
    # 指定模式
    "mode": "post",
} | ConfigManager("crawler_cookies/tiktok.yaml").get_config("tiktok")
# 实例化下载器和处理器
tkdownloader = TiktokDownloader(kwargs)
tkhandler = TiktokHandler(kwargs)

# 批量采集的用户ID
async def get_sec_user_ids():
    raw_urls = [
        "https://www.tiktok.com/@chinese_tiktok",
        ]
    # 对于单个URL
    # 提取有效URL
    urls = extract_valid_urls(raw_urls)

    # 对于URL列表
    return await SecUserIdFetcher.get_secuid(urls[0])

sec_user_ids = asyncio.run(get_sec_user_ids())

async def get_latest_post(sec_user_id: str, num_to_return: int = 4, user_path: str = 'test'):
    """
    获取用户最新发布的作品，跳过置顶视频

    Args:
        sec_user_id (str): 用户ID
        num_to_return (int): 返回最新的非置顶作品数量
        user_path (str): 保存路径
    """
    try:
        logger.debug(f"[bold green]开始获取用户ID：{sec_user_id} 的 {num_to_return} 个非置顶最新作品...[/bold green]")
        
        user_path = Path(user_path)
        user_path.mkdir(parents=True, exist_ok=True)
        
        collected_non_pinned_posts = 0
        cursor = "0"

        while collected_non_pinned_posts < num_to_return:
            async for aweme_list in tkhandler.fetch_user_post_videos(
                secUid=sec_user_id,
                cursor=cursor,
                min_cursor="0",  # 保持min_cursor为固定值
                page_counts=1,
                max_counts=20  # 增大max_counts以确保能获取足够的视频
            ):
                if not aweme_list:
                    logger.info(f"[bold yellow]无法获取更多用户作品信息:[/bold yellow] {sec_user_id}")
                    return
                try:
                    latest_post = aweme_list._to_list()
                    # 只更新cursor用于下一次请求
                    try:
                        cursor = aweme_list.cursor
                    except AttributeError:
                        # 如果没有cursor属性，说明已经到达最后一页
                        logger.info(f"[bold yellow]已获取所有可用视频，但只找到 {collected_non_pinned_posts} 个非置顶视频[/bold yellow]")
                        return

                except Exception as e:
                    logger.error(f"[bold red]获取用户作品信息失败:[/bold red] {e}")
                    return

                for video_data in latest_post:
                    is_pinned = video_data.get("isPinnedItem")

                    if not is_pinned:
                        await tkdownloader.create_download_tasks(
                            kwargs,
                            [video_data],
                            user_path=user_path
                        )
                        collected_non_pinned_posts += 1
                        logger.info(f"[bold green]已下载 {collected_non_pinned_posts}/{num_to_return} 个非置顶视频[/bold green]")

                        if collected_non_pinned_posts >= num_to_return:
                            return  # 已收集足够数量，返回
                
                # 如果当前页没有获取到足够的非置顶视频，需要继续获取下一页
                if collected_non_pinned_posts < num_to_return and (not cursor or cursor == "0"):
                    logger.info(f"[bold yellow]已获取所有可用视频，但只找到 {collected_non_pinned_posts} 个非置顶视频[/bold yellow]")
                    return

    except Exception as e:
        logger.error(traceback.format_exc())

async def main():
    """
    主函数，获取多个用户的最新作品
    """
    logger.info("[bold blue]开始获取用户的最新作品[/bold blue]")
    
    tasks = [get_latest_post(sec_user_ids)]
    await asyncio.gather(*tasks)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[bold yellow]程序已手动停止[/bold yellow]")
    except Exception as e:
        logger.error(f"[bold red]程序运行时出现异常: {e}[/bold red]")
        logger.error(traceback.format_exc())
