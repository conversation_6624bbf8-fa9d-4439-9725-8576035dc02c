import asyncio
from f2.apps.tiktok.handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from f2.apps.tiktok.utils import SecUserIdFetcher
from f2.utils.conf_manager import ConfigManager

# kwargs 的定义保持不变
kwargs = {
    "headers": {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0",
        "Referer": "https://www.tiktok.com/",
    },
    "timeout": 10,
}   | ConfigManager("crawler_cookies/tiktok.yaml").get_config("tiktok")

async def main():
    secUid = await SecUserIdFetcher.get_secuid("https://www.tiktok.com/@chinese_pandas")

    async for aweme_data_list in TiktokHandler(kwargs).fetch_user_post_videos(
        secUid, "0", "0", 1, 5
    ):
    #    print("=================_to_raw================")
        print(aweme_data_list._to_raw())
    video = await TiktokHandler(kwargs).fetch_one_video(itemId="7095819783324601605")
    print(video._to_raw())

if __name__ == "__main__":
    asyncio.run(main())
