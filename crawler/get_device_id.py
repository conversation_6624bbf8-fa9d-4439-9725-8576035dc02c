import asyncio
from f2.apps.tiktok.utils import DeviceIdManager


async def main():
    device_id = await DeviceIdManager.gen_device_id()
    print("device_id:", device_id.get("deviceId"), "cookie:", device_id.get("cookie"))
    device_id = await DeviceIdManager.gen_device_id(full_cookie=True)
    print("device_id:", device_id.get("deviceId"), "cookie:", device_id.get("cookie"))


if __name__ == "__main__":
    asyncio.run(main())
