import asyncio
import traceback

from f2.apps.douyin.handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from f2.apps.douyin.db import AsyncUserDB
from f2.apps.douyin.dl import DouyinDownloader
from f2.utils.conf_manager import ConfigManager
from f2.cli.cli_console import RichConsoleManager
from f2.log.logger import logger

# 全局配置参数，保护敏感信息
kwargs = {
    "headers": {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0",
        "Referer": "https://www.douyin.com/",
    },
    # 指定模式
    "mode": "post",
} | ConfigManager("crawler_cookies/douyin.yaml").get_config("douyin")
# 实例化下载器和处理器
dydownloader = DouyinDownloader(kwargs)
dyhandler = <PERSON><PERSON><PERSON>Handler(kwargs)

# 批量采集的用户ID
sec_user_ids = [
    "MS4wLjABAAAANXSltcLCzDGmdNFI2Q_QixVTr67NiYzjKOIP5s03CAE",
]


async def download_post(sec_user_id: str):
    """
    下载单个用户的所有作品

    Args:
        sec_user_id (str): 用户ID
    """

    try:
        logger.debug(
            f"[bold green]开始下载用户ID：{sec_user_id} 的作品...[/bold green]"
        )
        async with AsyncUserDB("douyin_users.db",) as audb:
            user_path = await dyhandler.get_or_add_user_data(kwargs.pop("headers"), sec_user_id, audb)
        print(user_path)
        async for aweme_list in dyhandler.fetch_user_post_videos(
            sec_user_id=sec_user_id,          
        ):
            print(aweme_list)
            if not aweme_list:
                logger.info(
                    f"[bold yellow]无法获取用户作品信息:[/bold yellow] {sec_user_id}"
                )
                return

            await dydownloader.create_download_tasks(
                kwargs, aweme_list._to_list(), user_path
            )

        logger.info(f"[bold green]用户ID：{sec_user_id} 作品下载完成。[/bold green]")
    except Exception as e:
        logger.error(traceback.format_exc())


async def main():
    """
    主函数，批量启动作品下载任务
    """
    logger.info("[bold blue]开始批量下载多个用户的作品[/bold blue]")

    semaphore = asyncio.Semaphore(kwargs.get("max_tasks", 5))
    async def limited_download(sec_user_id):
        async with semaphore:
            await download_post(sec_user_id) #

            #while True:  
            #    await download_post(sec_user_id)  
            #    await asyncio.sleep(1 * 60 * 60)  

    # 使用RichConsoleManager管理进度条
    with RichConsoleManager().progress:
        tasks = [
            asyncio.create_task(limited_download(sec_user_id))
            for sec_user_id in sec_user_ids
        ]
        await asyncio.gather(*tasks)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[bold yellow]程序已手动停止[/bold yellow]")
    except Exception as e:
        logger.error(f"[bold red]程序运行时出现异常: {e}[/bold red]")
        logger.error(traceback.format_exc())
