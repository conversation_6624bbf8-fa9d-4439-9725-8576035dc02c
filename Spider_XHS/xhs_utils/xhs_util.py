import json
import math
import random
import execjs
import os
from loguru import logger
import logging

# 尝试不同的导入路径
try:
    from Spider_XHS.xhs_utils.cookie_util import trans_cookies
except ImportError:
    try:
        from xhs_utils.cookie_util import trans_cookies
    except ImportError:
        logger.error("无法导入trans_cookies函数")
        def trans_cookies(cookies_str):
            # 简单的备用实现
            if not cookies_str:
                return {}
            cookies = {}
            for item in cookies_str.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookies[key] = value
            return cookies

# 获取当前文件目录的绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 计算static目录的路径
static_dir = os.path.join(os.path.dirname(current_dir), 'static')

try:
    js = execjs.compile(open(os.path.join(static_dir, 'xhs_xs_xsc_56.js'), 'r', encoding='utf-8').read())
except Exception as e:
    print(f"Error loading xhs_xs_xsc_56.js: {e}")
    js = None

try:
    xray_js = execjs.compile(open(os.path.join(static_dir, 'xhs_xray.js'), 'r', encoding='utf-8').read())
except Exception as e:
    print(f"Error loading xhs_xray.js: {e}")
    xray_js = None

def generate_x_b3_traceid(len=16):
    x_b3_traceid = ""
    for t in range(len):
        x_b3_traceid += "abcdef0123456789"[math.floor(16 * random.random())]
    return x_b3_traceid

def generate_xs_xs_common(a1, api, data=''):
    if js is None:
        logger.warning("JS文件无法加载，无法生成xs和xs_common")
        return "", "", ""
    ret = js.call('get_request_headers_params', api, data, a1)
    xs, xt, xs_common = ret['xs'], ret['xt'], ret['xs_common']
    return xs, xt, xs_common

def generate_xs(a1, api, data=''):
    if js is None:
        logger.warning("JS文件无法加载，无法生成xs")
        return "", ""
    ret = js.call('get_xs', api, data, a1)
    xs, xt = ret['X-s'], ret['X-t']
    return xs, xt

def generate_xray_traceid():
    if xray_js is None:
        logger.warning("xray_js文件无法加载，使用随机生成的traceid")
        # 生成一个模拟的xray traceid (16个字符)
        return generate_x_b3_traceid(16)
    try:
        return xray_js.call('traceId')
    except Exception as e:
        #logger.warning(f"调用xray_js.traceId失败: {e}，使用随机生成的traceid")
        return generate_x_b3_traceid(16)

def get_common_headers():
    return {
        "authority": "www.xiaohongshu.com",
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-language": "zh-CN,zh;q=0.9",
        "cache-control": "no-cache",
        "pragma": "no-cache",
        "referer": "https://www.xiaohongshu.com/",
        "sec-ch-ua": "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "document",
        "sec-fetch-mode": "navigate",
        "sec-fetch-site": "same-origin",
        "sec-fetch-user": "?1",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36"
    }

def get_request_headers_template():
    try:
        xray_traceid = generate_xray_traceid()
    except Exception as e:
        logger.warning(f"生成xray_traceid失败: {e}，使用随机生成的traceid")
        xray_traceid = generate_x_b3_traceid(16)
    
    return {
        "authority": "edith.xiaohongshu.com",
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "cache-control": "no-cache",
        "content-type": "application/json;charset=UTF-8",
        "origin": "https://www.xiaohongshu.com",
        "pragma": "no-cache",
        "referer": "https://www.xiaohongshu.com/",
        "sec-ch-ua": "\"Not A(Brand\";v=\"99\", \"Microsoft Edge\";v=\"121\", \"Chromium\";v=\"121\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0",
        "x-b3-traceid": "",
        "x-mns": "unload",
        "x-s": "",
        "x-s-common": "",
        "x-t": "",
        "x-xray-traceid": xray_traceid
    }

def generate_headers(a1, api, data=''):
    headers = get_request_headers_template()
    
    if js is None:
        # 如果js文件无法加载，返回基本headers
        logger.warning("JS文件无法加载，无法生成X-s相关参数")
        headers['x-b3-traceid'] = generate_x_b3_traceid()
        if data:
            data = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
        return headers, data
        
    xs, xt, xs_common = generate_xs_xs_common(a1, api, data)
    x_b3_traceid = generate_x_b3_traceid()
    headers['x-s'] = xs
    headers['x-t'] = str(xt)
    headers['x-s-common'] = xs_common
    headers['x-b3-traceid'] = x_b3_traceid
    if data:
        data = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
    return headers, data

def generate_request_params(cookies_str, api, data=''):
    cookies = trans_cookies(cookies_str)
    a1 = cookies.get('a1', '')
    headers, data = generate_headers(a1, api, data)
    return headers, cookies, data

def splice_str(api, params):
    url = api + '?'
    for key, value in params.items():
        if value is None:
            value = ''
        url += key + '=' + value + '&'
    return url[:-1]

