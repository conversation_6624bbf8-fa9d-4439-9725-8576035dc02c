import os
from loguru import logger
from dotenv import load_dotenv

def load_env():
    try:
        # 尝试加载.env文件
        load_dotenv()
        # 从环境变量获取cookies
        cookies_str = os.getenv('COOKIES')
        
        # 如果环境变量中没有cookies，使用默认值
        if not cookies_str:
            logger.warning("未找到COOKIES环境变量，使用默认值")
            cookies_str = "默认cookies值，请替换为有效的cookies"
            
        return cookies_str
    except Exception as e:
        logger.warning(f"加载环境变量时出错: {e}，使用默认cookies值")
        return "默认cookies值，请替换为有效的cookies"

def init():
    media_base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../datas/media_datas'))
    excel_base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../datas/excel_datas'))
    for base_path in [media_base_path, excel_base_path]:
        if not os.path.exists(base_path):
            os.makedirs(base_path)
            logger.info(f'创建目录 {base_path}')
    cookies_str = load_env()
    base_path = {
        'media': media_base_path,
        'excel': excel_base_path,
    }
    return cookies_str, base_path
