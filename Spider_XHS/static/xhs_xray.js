self = global;
window = global;
var zc666;
!function() {
    "use strict";
    var e, t, n, r, o, i = {}, u = {};
    function f(e) {
        var t = u[e];
        if (void 0 !== t)
            return t.exports;
        var n = u[e] = {
            id: e,
            loaded: !1,
            exports: {}
        };
        console.log(e);
        return i[e].call(n.exports, n, n.exports, f),
        n.loaded = !0,
        n.exports
    }
    f.m = i,
    f.amdO = {},
    e = [],
    f.O = function(t, n, r, o) {
        if (!n) {
            var i = 1 / 0;
            for (d = 0; d < e.length; d++) {
                n = e[d][0],
                r = e[d][1],
                o = e[d][2];
                for (var u = !0, c = 0; c < n.length; c++)
                    (!1 & o || i >= o) && Object.keys(f.O).every((function(e) {
                        return f.O[e](n[c])
                    }
                    )) ? n.splice(c--, 1) : (u = !1,
                    o < i && (i = o));
                if (u) {
                    e.splice(d--, 1);
                    var a = r();
                    void 0 !== a && (t = a)
                }
            }
            return t
        }
        o = o || 0;
        for (var d = e.length; d > 0 && e[d - 1][2] > o; d--)
            e[d] = e[d - 1];
        e[d] = [n, r, o]
    }
    ,
    f.n = function(e) {
        var t = e && e.__esModule ? function() {
            return e.default
        }
        : function() {
            return e
        }
        ;
        return f.d(t, {
            a: t
        }),
        t
    }
    ,
    n = Object.getPrototypeOf ? function(e) {
        return Object.getPrototypeOf(e)
    }
    : function(e) {
        return e.__proto__
    }
    ,
    f.t = function(e, r) {
        if (1 & r && (e = this(e)),
        8 & r)
            return e;
        if ("object" == typeof e && e) {
            if (4 & r && e.__esModule)
                return e;
            if (16 & r && "function" == typeof e.then)
                return e
        }
        var o = Object.create(null);
        f.r(o);
        var i = {};
        t = t || [null, n({}), n([]), n(n)];
        for (var u = 2 & r && e; "object" == typeof u && !~t.indexOf(u); u = n(u))
            Object.getOwnPropertyNames(u).forEach((function(t) {
                i[t] = function() {
                    return e[t]
                }
            }
            ));
        return i.default = function() {
            return e
        }
        ,
        f.d(o, i),
        o
    }
    ,
    f.d = function(e, t) {
        for (var n in t)
            f.o(t, n) && !f.o(e, n) && Object.defineProperty(e, n, {
                enumerable: !0,
                get: t[n]
            })
    }
    ,
    f.f = {},
    f.e = function(e) {
        return Promise.all(Object.keys(f.f).reduce((function(t, n) {
            return f.f[n](e, t),
            t
        }
        ), []))
    }
    ,
    f.u = function(e) {
        return "js/" + ({
            41: "Board",
            94: "Login",
            256: "NPS",
            290: "Notification",
            406: "User",
            464: "FeedToNote",
            540: "Explore",
            692: "Track",
            763: "Search",
            891: "xhs-web-player",
            895: "Note",
            898: "minor"
        }[e] || e) + "." + {
            13: "849e078",
            18: "88c4016",
            41: "a4fad25",
            64: "de4ace7",
            92: "1b9e4df",
            94: "01eead2",
            168: "256b43c",
            256: "3c5b745",
            281: "ade9f6a",
            290: "d0e6310",
            334: "afb0229",
            337: "e738619",
            398: "80ce566",
            406: "0477db9",
            426: "fd994fa",
            464: "073bfcc",
            469: "a49ea26",
            474: "738cddb",
            494: "c852c82",
            513: "7ca0915",
            540: "f44da86",
            563: "5fc3402",
            588: "67edf6f",
            591: "ddde7d9",
            692: "0c3ac5e",
            699: "c290318",
            737: "9268c58",
            763: "01c6b25",
            766: "f0a8354",
            772: "50c8fcf",
            787: "385b767",
            871: "d5ef805",
            891: "e811881",
            895: "697ec77",
            898: "868733b"
        }[e] + ".chunk.js"
    }
    ,
    f.miniCssF = function(e) {
        return "css/" + ({
            41: "Board",
            94: "Login",
            256: "NPS",
            290: "Notification",
            406: "User",
            464: "FeedToNote",
            540: "Explore",
            763: "Search",
            895: "Note",
            898: "minor"
        }[e] || e) + "." + {
            41: "b232e5e",
            92: "95cabbe",
            94: "b4971ae",
            256: "5d4f927",
            290: "efde4b1",
            334: "0f69949",
            337: "919c828",
            398: "ffe8b37",
            406: "e3c28d5",
            426: "082db25",
            464: "1bbfe82",
            540: "d6040d3",
            588: "3e8b57e",
            763: "af3c4cd",
            895: "98f4076",
            898: "5a4e17f"
        }[e] + ".chunk.css"
    }
    ,
    f.g = function() {
        if ("object" == typeof globalThis)
            return globalThis;
        try {
            return this || new Function("return this")()
        } catch (e) {
            if ("object" == typeof window)
                return window
        }
    }(),
    f.o = function(e, t) {
        return Object.prototype.hasOwnProperty.call(e, t)
    }
    ,
    r = {},
    o = "xhs-pc-web:",
    f.l = function(e, t, n, i) {
        if (r[e])
            r[e].push(t);
        else {
            var u, c;
            if (void 0 !== n)
                for (var a = document.getElementsByTagName("script"), d = 0; d < a.length; d++) {
                    var l = a[d];
                    if (l.getAttribute("src") == e || l.getAttribute("data-webpack") == o + n) {
                        u = l;
                        break
                    }
                }
            u || (c = !0,
            (u = document.createElement("script")).charset = "utf-8",
            u.timeout = 120,
            f.nc && u.setAttribute("nonce", f.nc),
            u.setAttribute("data-webpack", o + n),
            u.src = e),
            r[e] = [t];
            var s = function(t, n) {
                u.onerror = u.onload = null,
                clearTimeout(b);
                var o = r[e];
                if (delete r[e],
                u.parentNode && u.parentNode.removeChild(u),
                o && o.forEach((function(e) {
                    return e(n)
                }
                )),
                t)
                    return t(n)
            }
              , b = setTimeout(s.bind(null, void 0, {
                type: "timeout",
                target: u
            }), 12e4);
            u.onerror = s.bind(null, u.onerror),
            u.onload = s.bind(null, u.onload),
            c && document.head.appendChild(u)
        }
    }
    ,
    f.r = function(e) {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
            value: "Module"
        }),
        Object.defineProperty(e, "__esModule", {
            value: !0
        })
    }
    ,
    f.nmd = function(e) {
        return e.paths = [],
        e.children || (e.children = []),
        e
    }
    ,
    f.p = "//fe-static.xhscdn.com/formula-static/xhs-pc-web/public/",
    function() {
        if ("undefined" != typeof document) {
            var e = function(e) {
                return new Promise((function(t, n) {
                    var r = f.miniCssF(e)
                      , o = f.p + r;
                    if (function(e, t) {
                        for (var n = document.getElementsByTagName("link"), r = 0; r < n.length; r++) {
                            var o = (u = n[r]).getAttribute("data-href") || u.getAttribute("href");
                            if ("stylesheet" === u.rel && (o === e || o === t))
                                return u
                        }
                        var i = document.getElementsByTagName("style");
                        for (r = 0; r < i.length; r++) {
                            var u;
                            if ((o = (u = i[r]).getAttribute("data-href")) === e || o === t)
                                return u
                        }
                    }(r, o))
                        return t();
                    !function(e, t, n, r, o) {
                        var i = document.createElement("link");
                        i.rel = "stylesheet",
                        i.type = "text/css",
                        f.nc && (i.nonce = f.nc),
                        i.onerror = i.onload = function(n) {
                            if (i.onerror = i.onload = null,
                            "load" === n.type)
                                r();
                            else {
                                var u = n && n.type
                                  , f = n && n.target && n.target.href || t
                                  , c = new Error("Loading CSS chunk " + e + " failed.\n(" + u + ": " + f + ")");
                                c.name = "ChunkLoadError",
                                c.code = "CSS_CHUNK_LOAD_FAILED",
                                c.type = u,
                                c.request = f,
                                i.parentNode && i.parentNode.removeChild(i),
                                o(c)
                            }
                        }
                        ,
                        i.href = t,
                        n ? n.parentNode.insertBefore(i, n.nextSibling) : document.head.appendChild(i)
                    }(e, o, null, t, n)
                }
                ))
            }
              , t = {
                577: 0
            };
            f.f.miniCss = function(n, r) {
                t[n] ? r.push(t[n]) : 0 !== t[n] && {
                    41: 1,
                    92: 1,
                    94: 1,
                    256: 1,
                    290: 1,
                    334: 1,
                    337: 1,
                    398: 1,
                    406: 1,
                    426: 1,
                    464: 1,
                    540: 1,
                    588: 1,
                    763: 1,
                    895: 1,
                    898: 1
                }[n] && r.push(t[n] = e(n).then((function() {
                    t[n] = 0
                }
                ), (function(e) {
                    throw delete t[n],
                    e
                }
                )))
            }
        }
    }(),
    function() {
        var e = {
            577: 0
        };
        f.f.j = function(t, n) {
            var r = f.o(e, t) ? e[t] : void 0;
            if (0 !== r)
                if (r)
                    n.push(r[2]);
                else if (577 != t) {
                    var o = new Promise((function(n, o) {
                        r = e[t] = [n, o]
                    }
                    ));
                    n.push(r[2] = o);
                    var i = f.p + f.u(t)
                      , u = new Error;
                    f.l(i, (function(n) {
                        if (f.o(e, t) && (0 !== (r = e[t]) && (e[t] = void 0),
                        r)) {
                            var o = n && ("load" === n.type ? "missing" : n.type)
                              , i = n && n.target && n.target.src;
                            u.message = "Loading chunk " + t + " failed.\n(" + o + ": " + i + ")",
                            u.name = "ChunkLoadError",
                            u.type = o,
                            u.request = i,
                            r[1](u)
                        }
                    }
                    ), "chunk-" + t, t)
                } else
                    e[t] = 0
        }
        ,
        f.O.j = function(t) {
            return 0 === e[t]
        }
        ;
        var t = function(t, n) {
            var r, o, i = n[0], u = n[1], c = n[2], a = 0;
            if (i.some((function(t) {
                return 0 !== e[t]
            }
            ))) {
                for (r in u)
                    f.o(u, r) && (f.m[r] = u[r]);
                if (c)
                    var d = c(f)
            }
            for (t && t(n); a < i.length; a++)
                o = i[a],
                f.o(e, o) && e[o] && e[o][0](),
                e[o] = 0;
            return f.O(d)
        }
          , n = self.webpackChunkxhs_pc_web = self.webpackChunkxhs_pc_web || [];
        n.forEach(t.bind(null, 0)),
        n.push = t.bind(null, n.push.bind(n))
    }()
    zc666 = f;
}();
//# sourceMappingURL=https://picasso-private-1251524319.cos.ap-shanghai.myqcloud.com/data/formula-static/formula/xhs-pc-web/runtime-main.8718828.js.map
try {
    require(require('path').join(__dirname, 'xhs_xray_pack1.js'));
} catch (e) {
    try {
        require(require('path').join(__dirname, 'xhs_xray_pack1.js'));
    } catch (e) {
        try {
            require(require('path').join(__dirname, 'xhs_xray_pack1.js'));
        } catch (e) {
            console.error('无法加载xhs_xray_pack1.js:', e);
        }
    }
}
try {
    require('./xhs_xray_pack2.js');
} catch (e) {
    try {
        require('../static/xhs_xray_pack2.js');
    } catch (e) {
        require('./static/xhs_xray_pack2.js');
    }
}
var n = zc666(36497)
          , o = zc666(609)
          , i = zc666(2030);
var a = zc666(81422)
          , u = zc666(49600);

traceId = function() {
    var t, e, r, s = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : i();
    return o(t = "".concat(n(e = u.fromNumber(s, !0).shiftLeft(23).or(a.Int.seq()).toString(16)).call(e, 16, "0"))).call(t, n(r = new u(a.Int.random(32),a.Int.random(32),!0).toString(16)).call(r, 16, "0"))
}
