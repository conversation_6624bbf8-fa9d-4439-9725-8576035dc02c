import os
import argparse
from loguru import logger
from Spider_XHS.apis.xhs_pc_apis import XHS_Apis
from Spider_XHS.xhs_utils.common_util import init
from Spider_XHS.xhs_utils.data_util import handle_note_info, download_note, save_to_xlsx

def monitor_user_note(cookies_str=None):
    if not cookies_str:
        cookies_str, base_path = init()
    else:
        _, base_path = init()
        
    xhs_apis = XHS_Apis()
    note_list = []
    user_url = 'https://www.xiaohongshu.com/user/profile/5657d81c7c5bb814405de598?xsec_token=ABahi1DhOHYqIpJoxYePpD9gzqE8uynoN4bMRLxyctoGI=&xsec_source=pc_feed'
    user_id = "565e638bcb35fb1b0c287ac7"
    success, msg, note_info = xhs_apis.get_user_note_info(user_id, "", cookies_str)
    
    if not success or note_info is None or 'data' not in note_info:
        logger.error(f"获取笔记信息失败: {msg}")
        return
    
    if 'notes' not in note_info.get('data', {}):
        logger.error("笔记信息中没有notes字段")
        return
    
    print(f'用户 {user_id} 作品数量: {len(note_info.get("data").get("notes"))}')
    
    for simple_note_info in note_info.get("data").get("notes"):
        try:
            interact_info = simple_note_info.get("interact_info", {})
            is_top = interact_info.get("sticky") if interact_info else None
            media_type = simple_note_info.get("type")
            print("是否置顶：", is_top)
            print("媒体类型：", media_type)
            
            note_id = simple_note_info.get('note_id')
            xsec_token = simple_note_info.get('xsec_token', '')
            
            if note_id:
                note_url = f"https://www.xiaohongshu.com/explore/{note_id}?xsec_token={xsec_token}"
                print("笔记地址：", note_url)
                note_list.append(note_url)
            else:
                logger.warning("笔记ID不存在")
        except Exception as e:
            logger.error(f"处理笔记信息出错: {str(e)}")

def spider_some_note(self, notes: list, cookies_str: str, base_path: dict, save_choice: str, excel_name: str = '', proxies=None):
    """
    爬取笔记数据
    :param notes:
    :param cookies_str:
    :param base_path:
    :return:
    """
    if (save_choice == 'all' or save_choice == 'excel') and excel_name == '':
        raise ValueError('excel_name 不能为空')
    note_list = []
    for note_url in notes:
        success, msg, note_info = self.spider_note(note_url, cookies_str, proxies)
        if note_info is not None and success:
            note_list.append(note_info)
    for note_info in note_list:
        if save_choice == 'all' or save_choice == 'media':
            download_note(note_info, base_path['media'])
    if save_choice == 'all' or save_choice == 'excel':
        file_path = os.path.abspath(os.path.join(base_path['excel'], f'{excel_name}.xlsx'))
        save_to_xlsx(note_list, file_path)


if __name__ == '__main__':
    
    monitor_user_note()
