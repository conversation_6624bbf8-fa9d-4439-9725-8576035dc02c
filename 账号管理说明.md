# 小红书监控系统 - 账号管理说明

## 📋 账号类型说明

### 1. 监控账号（Monitor Account）
**用途**：用于访问小红书API，获取用户作品信息

**配置要求**：
- ✅ **需要cookies**：从浏览器获取的有效登录cookies
- ❌ **不需要user_id**：这个账号本身不是被监控对象
- 📝 **账号类型**：选择"monitor"
- 🔧 **平台**：选择"xiaohongshu"

**示例配置**：
```
账号名称: 小红书API访问账号
平台: xiaohongshu
账号类型: monitor
Cookies: [从浏览器复制的完整cookie字符串]
用户ID: [留空]
```

### 2. 被监控用户（Target User）
**用途**：我们要监控其作品的目标用户

**配置方式**：
- ❌ **不在账号管理中添加**
- ✅ **在创建监控任务时填写user_id**
- 📝 **获取方式**：从用户主页URL中提取

**示例**：
- 用户主页：`https://www.xiaohongshu.com/user/profile/565e638bcb35fb1b0c287ac7`
- 提取user_id：`565e638bcb35fb1b0c287ac7`

### 3. 上传账号（Upload Account）
**用途**：用于将下载的内容上传到目标平台

**配置要求**：
- ✅ **需要cookies或API密钥**：根据目标平台要求
- ✅ **可能需要user_id**：某些平台需要
- 📝 **账号类型**：选择"upload"
- 🔧 **平台**：选择目标平台（youtube、tiktok等）

## 🔄 工作流程图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   监控账号       │    │   被监控用户     │    │   上传账号       │
│  (Monitor)      │    │  (Target User)  │    │  (Upload)       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ 需要: cookies   │    │ 需要: user_id   │    │ 需要: cookies   │
│ 用于: API访问   │    │ 用于: 指定目标   │    │ 用于: 内容上传   │
│ 配置: 账号管理   │    │ 配置: 创建任务   │    │ 配置: 账号管理   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    监控任务执行流程                              │
├─────────────────────────────────────────────────────────────────┤
│ 1. 使用监控账号的cookies访问小红书API                           │
│ 2. 获取被监控用户(user_id)的最新作品                           │
│ 3. 过滤置顶和非视频内容                                         │
│ 4. 下载符合条件的视频                                           │
│ 5. (可选)使用上传账号上传到目标平台                             │
└─────────────────────────────────────────────────────────────────┘
```

## 📝 配置步骤

### 步骤1：添加监控账号
1. 访问 http://127.0.0.1:8888/accounts
2. 点击"添加账号"
3. 填写信息：
   ```
   账号名称: 我的小红书监控账号
   平台: xiaohongshu
   账号类型: monitor
   Cookies: [从浏览器复制]
   用户ID: [留空]
   ```

### 步骤2：(可选)添加上传账号
如果需要自动上传到其他平台：
1. 继续在账号管理页面添加
2. 填写目标平台信息：
   ```
   账号名称: YouTube上传账号
   平台: youtube
   账号类型: upload
   Cookies: [YouTube的认证信息]
   ```

### 步骤3：创建监控任务
1. 访问 http://127.0.0.1:8888/xiaohongshu
2. 点击"创建监控任务"
3. 填写任务信息：
   ```
   任务名称: 监控某用户视频
   被监控用户ID: 565e638bcb35fb1b0c287ac7  ← 这里填写要监控的用户
   监控账号: 选择步骤1创建的监控账号      ← 用于API访问
   目标上传账号: 选择步骤2创建的上传账号   ← 用于上传(可选)
   ```

## ⚠️ 常见误区

### ❌ 错误理解
- 认为需要在账号管理中添加被监控用户
- 混淆监控账号和被监控用户的概念
- 认为被监控用户需要提供cookies

### ✅ 正确理解
- **监控账号**：你自己的小红书账号，用于API访问，需要cookies
- **被监控用户**：你要监控的目标用户，只需要user_id，不需要其cookies
- **上传账号**：目标平台的账号，用于上传内容，需要相应认证信息

## 🔍 获取Cookies方法

### 小红书Cookies获取
1. 打开浏览器，访问 https://www.xiaohongshu.com
2. 登录你的小红书账号
3. 按F12打开开发者工具
4. 切换到"Network"标签
5. 刷新页面或点击任意链接
6. 找到任意请求，查看Request Headers
7. 复制完整的Cookie值

### 示例Cookie格式
```
a1=...; webId=...; gid=...; web_session=...; xsecappid=...
```

## 📞 技术支持

如果在账号配置过程中遇到问题：

1. **监控账号无法访问API**
   - 检查cookies是否有效
   - 确认账号未被限制
   - 尝试重新获取cookies

2. **找不到被监控用户**
   - 确认user_id格式正确
   - 检查用户是否存在
   - 确认用户主页是公开的

3. **上传失败**
   - 检查目标平台账号配置
   - 确认上传权限
   - 查看错误日志

---

**💡 记住：监控账号需要cookies，被监控用户只需要user_id！**
