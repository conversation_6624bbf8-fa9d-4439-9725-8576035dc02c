# 简化版多平台内容同步系统 - 项目结构

```
video_sync_system/
├── README.md
├── requirements.txt
├── .env.example
├── .gitignore
├── main.py                       # 应用入口
│
├── core/                         # 核心模块
│   ├── __init__.py
│   ├── task_manager.py           # 任务管理器
│   ├── scheduler.py              # 调度器
│   └── database.py               # 数据库操作
│
├── platforms/                    # 平台适配器
│   ├── __init__.py
│   ├── base.py                   # 基础适配器类
│   ├── tiktok.py                 # TikTok适配器
│   ├── douyin.py                 # 抖音适配器
│   ├── xiaohongshu.py            # 小红书适配器
│   ├── bilibili.py               # B站适配器
│   └── youtube.py                # YouTube适配器
│
├── models/                       # 数据模型
│   ├── __init__.py
│   ├── task.py                   # 任务模型
│   └── account.py                # 账号模型
│
├── utils/                        # 工具函数
│   ├── __init__.py
│   ├── logger.py                 # 日志工具
│   ├── config.py                 # 配置管理
│   └── file_utils.py             # 文件工具
│
├── data/                         # 数据目录
│   ├── database.db               # SQLite数据库
│   ├── downloads/                # 下载文件
│   ├── logs/                     # 日志文件
│   └── configs/                  # 配置文件
│
└── legacy/                       # 现有代码
    ├── Spider_XHS/               # 现有小红书爬虫
    ├── crawler/                  # 现有TikTok/抖音爬虫
    └── upload_scripts/           # 现有上传脚本
```

## 核心设计原则

### 1. 模块化设计
- 每个平台适配器独立实现
- 核心服务与平台适配器解耦
- 便于新平台接入和功能扩展

### 2. 数据驱动
- 任务配置存储在数据库
- 账号信息加密存储
- 内容同步记录可追溯

### 3. 异步处理
- 使用消息队列处理任务
- 支持并发监控和上传
- 避免阻塞主线程

### 4. 错误处理
- 完善的重试机制
- 详细的错误日志
- 任务状态实时更新

### 5. 安全性
- 账号信息加密存储
- API访问权限控制
- 敏感操作审计日志
