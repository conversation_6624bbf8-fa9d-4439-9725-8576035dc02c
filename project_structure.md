# 多平台内容同步系统 - 项目结构

```
video_sync_system/
├── README.md
├── requirements.txt
├── docker-compose.yml
├── .env.example
├── .gitignore
│
├── app/                          # 主应用目录
│   ├── __init__.py
│   ├── main.py                   # 应用入口
│   ├── config/                   # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py           # 配置类
│   │   └── database.py           # 数据库配置
│   │
│   ├── core/                     # 核心服务
│   │   ├── __init__.py
│   │   ├── task_manager.py       # 任务管理器
│   │   ├── scheduler.py          # 调度管理器
│   │   ├── account_manager.py    # 账号管理器
│   │   └── content_processor.py  # 内容处理器
│   │
│   ├── adapters/                 # 平台适配器
│   │   ├── __init__.py
│   │   ├── base/                 # 基础适配器
│   │   │   ├── __init__.py
│   │   │   ├── monitor_adapter.py    # 监控适配器基类
│   │   │   └── upload_adapter.py     # 上传适配器基类
│   │   │
│   │   ├── monitors/             # 监控适配器
│   │   │   ├── __init__.py
│   │   │   ├── tiktok_monitor.py
│   │   │   ├── douyin_monitor.py
│   │   │   ├── xiaohongshu_monitor.py
│   │   │   └── bilibili_monitor.py
│   │   │
│   │   └── uploaders/            # 上传适配器
│   │       ├── __init__.py
│   │       ├── tiktok_uploader.py
│   │       ├── youtube_uploader.py
│   │       ├── xiaohongshu_uploader.py
│   │       └── bilibili_uploader.py
│   │
│   ├── models/                   # 数据模型
│   │   ├── __init__.py
│   │   ├── task.py               # 任务模型
│   │   ├── account.py            # 账号模型
│   │   ├── content.py            # 内容模型
│   │   └── sync_record.py        # 同步记录模型
│   │
│   ├── api/                      # API接口
│   │   ├── __init__.py
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── tasks.py          # 任务相关API
│   │   │   ├── accounts.py       # 账号相关API
│   │   │   └── contents.py       # 内容相关API
│   │   └── dependencies.py       # API依赖
│   │
│   ├── utils/                    # 工具函数
│   │   ├── __init__.py
│   │   ├── logger.py             # 日志工具
│   │   ├── retry.py              # 重试机制
│   │   ├── file_handler.py       # 文件处理
│   │   ├── crypto.py             # 加密工具
│   │   └── validators.py         # 验证工具
│   │
│   └── web/                      # Web界面
│       ├── __init__.py
│       ├── static/               # 静态文件
│       ├── templates/            # 模板文件
│       └── routes.py             # Web路由
│
├── tests/                        # 测试目录
│   ├── __init__.py
│   ├── test_core/
│   ├── test_adapters/
│   └── test_utils/
│
├── scripts/                      # 脚本目录
│   ├── init_db.py               # 数据库初始化
│   ├── migrate.py               # 数据迁移
│   └── deploy.py                # 部署脚本
│
├── data/                         # 数据目录
│   ├── downloads/               # 下载文件
│   ├── uploads/                 # 上传文件
│   ├── logs/                    # 日志文件
│   └── configs/                 # 配置文件
│       ├── accounts/            # 账号配置
│       └── platforms/           # 平台配置
│
├── legacy/                       # 现有代码迁移
│   ├── Spider_XHS/              # 现有小红书爬虫
│   ├── crawler/                 # 现有TikTok/抖音爬虫
│   └── upload_scripts/          # 现有上传脚本
│
└── docs/                         # 文档目录
    ├── api.md                   # API文档
    ├── deployment.md            # 部署文档
    └── development.md           # 开发文档
```

## 核心设计原则

### 1. 模块化设计
- 每个平台适配器独立实现
- 核心服务与平台适配器解耦
- 便于新平台接入和功能扩展

### 2. 数据驱动
- 任务配置存储在数据库
- 账号信息加密存储
- 内容同步记录可追溯

### 3. 异步处理
- 使用消息队列处理任务
- 支持并发监控和上传
- 避免阻塞主线程

### 4. 错误处理
- 完善的重试机制
- 详细的错误日志
- 任务状态实时更新

### 5. 安全性
- 账号信息加密存储
- API访问权限控制
- 敏感操作审计日志
