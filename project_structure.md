# 简化版多平台内容同步系统 - 项目结构

```
video_sync_system/
├── README.md
├── requirements.txt
├── .env.example
├── .gitignore
├── main.py                       # 应用入口
│
├── core/                         # 核心模块
│   ├── __init__.py
│   ├── task_manager.py           # 任务管理器
│   ├── scheduler.py              # 调度器
│   └── database.py               # 数据库操作
│
├── platforms/                    # 平台适配器
│   ├── __init__.py
│   ├── base.py                   # 基础适配器类
│   ├── tiktok.py                 # TikTok适配器
│   ├── douyin.py                 # 抖音适配器
│   ├── xiaohongshu.py            # 小红书适配器
│   ├── bilibili.py               # B站适配器
│   └── youtube.py                # YouTube适配器
│
├── models/                       # 数据模型
│   ├── __init__.py
│   ├── task.py                   # 任务模型
│   └── account.py                # 账号模型
│
├── utils/                        # 工具函数
│   ├── __init__.py
│   ├── logger.py                 # 日志工具
│   ├── config.py                 # 配置管理
│   └── file_utils.py             # 文件工具
│
├── data/                         # 数据目录
│   ├── database.db               # SQLite数据库
│   ├── downloads/                # 下载文件
│   ├── logs/                     # 日志文件
│   └── configs/                  # 配置文件
│
└── legacy/                       # 现有代码
    ├── Spider_XHS/               # 现有小红书爬虫
    ├── crawler/                  # 现有TikTok/抖音爬虫
    └── upload_scripts/           # 现有上传脚本
```

## 简化设计原则

### 1. 简单易用
- 单文件启动，配置简单
- SQLite数据库，无需额外安装
- 命令行界面，操作直观

### 2. 模块化
- 每个平台一个适配器文件
- 核心功能独立模块
- 便于维护和扩展

### 3. 可靠性
- 基础的重试机制
- 详细的日志记录
- 任务状态跟踪

### 4. 安全性
- 配置文件存储敏感信息
- 基础的数据加密
- 操作日志记录
