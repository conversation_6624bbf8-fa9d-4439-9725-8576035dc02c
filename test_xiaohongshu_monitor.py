#!/usr/bin/env python3
"""
测试小红书增强监控器
"""

import os
import sys
import asyncio
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'video_sync_system'))

from models.account import Account, PlatformType, AccountType
from platforms.xiaohongshu_enhanced_monitor import XiaohongshuEnhancedMonitor


async def test_xiaohongshu_monitor():
    """测试小红书监控功能"""
    
    # 创建测试账号（需要有效的cookies）
    test_account = Account(
        id="test_xhs_account",
        name="测试小红书账号",
        platform=PlatformType.XIAOHONGSHU,
        account_type=AccountType.MONITOR,
        cookies="",  # 这里需要填入有效的cookies
        config={}
    )
    
    # 测试用户ID（从您的示例中获取）
    test_user_id = "565e638bcb35fb1b0c287ac7"
    
    try:
        # 创建增强监控器
        monitor = XiaohongshuEnhancedMonitor(test_account)
        print(f"✅ 增强监控器创建成功")
        
        # 测试监控功能
        print(f"\n🔍 开始监控用户 {test_user_id} 的最新视频作品...")
        
        content_items = await monitor.monitor_user_latest_videos(
            user_id=test_user_id,
            limit=10,
            exclude_pinned=True,  # 排除置顶
            video_only=True       # 只要视频
        )
        
        print(f"\n📊 监控结果:")
        print(f"   - 总共找到 {len(content_items)} 条符合条件的视频作品")
        
        if content_items:
            print(f"\n📝 作品详情:")
            for i, item in enumerate(content_items, 1):
                print(f"   {i}. 标题: {item.title}")
                print(f"      ID: {item.id}")
                print(f"      URL: {item.url}")
                print(f"      类型: {item.content_type}")
                print(f"      是否置顶: {item.extra_data.get('is_pinned', False)}")
                print(f"      媒体类型: {item.extra_data.get('media_type', 'unknown')}")
                print(f"      缩略图: {item.thumbnail_url}")
                print()
        
        # 测试下载功能（可选）
        if content_items and input("\n是否测试下载功能？(y/N): ").lower() == 'y':
            download_dir = "test_downloads"
            os.makedirs(download_dir, exist_ok=True)
            
            print(f"\n⬇️ 开始下载到 {download_dir}...")
            
            downloaded_files = await monitor.download_filtered_videos(
                content_items[:2],  # 只下载前2个作品
                download_dir
            )
            
            print(f"✅ 下载完成，共 {len(downloaded_files)} 个文件:")
            for file_path in downloaded_files:
                print(f"   - {file_path}")
        
        print(f"\n🎉 测试完成！")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保Spider_XHS模块已正确安装和配置")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_with_real_cookies():
    """使用真实cookies进行测试"""
    
    print("🔧 小红书增强监控器测试")
    print("=" * 50)
    
    # 检查是否有cookies文件
    cookies_file = "xhs_cookies.txt"
    if os.path.exists(cookies_file):
        with open(cookies_file, 'r', encoding='utf-8') as f:
            cookies = f.read().strip()
        print(f"✅ 从 {cookies_file} 读取cookies")
    else:
        cookies = input("请输入小红书cookies (或创建xhs_cookies.txt文件): ").strip()
        if not cookies:
            print("❌ 没有提供cookies，无法进行测试")
            return
    
    # 更新测试账号的cookies
    test_account = Account(
        id="test_xhs_account",
        name="测试小红书账号",
        platform=PlatformType.XIAOHONGSHU,
        account_type=AccountType.MONITOR,
        cookies=cookies,
        config={}
    )
    
    # 运行异步测试
    asyncio.run(test_xiaohongshu_monitor_with_account(test_account))


async def test_xiaohongshu_monitor_with_account(account):
    """使用指定账号测试监控功能"""
    
    # 测试用户ID
    test_user_id = "565e638bcb35fb1b0c287ac7"
    
    try:
        # 创建增强监控器
        monitor = XiaohongshuEnhancedMonitor(account)
        print(f"✅ 增强监控器创建成功")
        
        # 测试监控功能
        print(f"\n🔍 开始监控用户 {test_user_id} 的最新视频作品...")
        print(f"   - 排除置顶作品: ✅")
        print(f"   - 只获取视频: ✅")
        print(f"   - 获取数量限制: 10")
        
        content_items = await monitor.monitor_user_latest_videos(
            user_id=test_user_id,
            limit=10,
            exclude_pinned=True,
            video_only=True
        )
        
        print(f"\n📊 监控结果:")
        print(f"   - 符合条件的视频作品: {len(content_items)} 条")
        
        if content_items:
            print(f"\n📝 作品列表:")
            for i, item in enumerate(content_items, 1):
                extra_data = item.extra_data or {}
                print(f"   {i}. {item.title or '无标题'}")
                print(f"      📍 URL: {item.url}")
                print(f"      🎬 类型: {item.content_type}")
                print(f"      📌 置顶: {'是' if extra_data.get('is_pinned') else '否'}")
                print(f"      🎥 媒体类型: {extra_data.get('media_type', 'unknown')}")
                if item.thumbnail_url:
                    print(f"      🖼️ 缩略图: {item.thumbnail_url}")
                print()
        else:
            print("   ⚠️ 没有找到符合条件的视频作品")
        
        print(f"🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 启动小红书增强监控器测试")
    
    # 检查依赖
    try:
        import sys
        sys.path.append('Spider_XHS')
        from Spider_XHS.apis.xhs_pc_apis import XHS_Apis
        print("✅ Spider_XHS 模块可用")
    except ImportError:
        print("❌ Spider_XHS 模块不可用，请检查安装")
        sys.exit(1)
    
    # 运行测试
    test_with_real_cookies()
