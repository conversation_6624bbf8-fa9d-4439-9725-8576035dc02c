# .gitignore (Comprehensive General Template)

# -----------------------------------------------------------------------------
# 一般性忽略 (General Ignores)
# -----------------------------------------------------------------------------

# 编译产物和打包文件 (Compiled artifacts and packaged files)
*.o
*.a
*.so
*.dll
*.lib
*.class
*.jar
*.war
*.ear
*.exe
*.out
*.app
*.deb
*.rpm
*.msi
*.msm
*.msp

# 构建目录 (Build directories)
build/
dist/
target/
bin/
obj/
out/

# 日志文件 (Log files)
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*-debug.log
*-error.log

# 临时文件和备份文件 (Temporary and backup files)
*~
*.tmp
*.bak
*.swp
*.swo
.#*
.~lock.*

# 压缩包 (Archives)
*.tar
*.gz
*.zip
*.rar
*.7z

# -----------------------------------------------------------------------------
# 操作系统特定文件 (Operating System Specific)
# -----------------------------------------------------------------------------

# macOS
.DS_Store
.AppleDouble
.LSOverride
._*
.Spotlight-V100
.Trashes
Network Trash Folder
Temporary Items

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk

# Linux (often editor specific but common)
*~
.*.swp

# -----------------------------------------------------------------------------
# IDE 和编辑器特定文件 (IDE and Editor Specific)
# -----------------------------------------------------------------------------

# IntelliJ / PyCharm / WebStorm / Android Studio etc. (JetBrains)
.idea/
*.iml
*.ipr
*.iws
shelf/ # IntelliJ/PyCharm shelves
# 如果你想共享某些 .idea 配置，可以取消注释并调整下面的内容
# !.idea/artifacts.xml
# !.idea/compiler.xml
# !.idea/copyright/
# !.idea/dictionaries/
# !.idea/encodings.xml
# !.idea/misc.xml
# !.idea/modules.xml
# !.idea/runConfigurations/ # 通常不共享，除非对团队有通用性
# !.idea/vcs.xml
# !.idea/workspace.xml # 通常不共享

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/c_cpp_properties.json # Example for C/C++

# Visual Studio (Windows)
*.suo
*.user
*.sln.docstates
*.vspscc
*.vssscc
Generated\ Files/
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.pch
*.pdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.log
*.scc

# Eclipse
.project
.classpath
.settings/
*.pydevproject

# NetBeans
nbproject/
nbactions.xml
nbbuild.xml
nb-configuration.xml
build.properties # Usually project specific, not Netbeans config
build-impl.xml

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/
project.cson # Project specific config
compile-cache/
storage/

# Vim
Session.vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-w][a-z]

# -----------------------------------------------------------------------------
# 语言和框架特定文件 (Language and Framework Specific)
# -----------------------------------------------------------------------------

# Python
__pycache__/
*.py[cod]
*$py.class
.Python
*.egg-info/
*.egg
MANIFEST
# Python Virtual Environments
.env
.venv/
venv/
ENV/
env/
# Python Build artifacts
# build/ # 已在通用部分
# dist/  # 已在通用部分
# develop-eggs/
# eggs/
# lib/
# lib64/
# parts/
# sdist/
# var/
# wheels/
# share/python-wheels/
# .installed.cfg
# Python test artifacts
.coverage
.coverage.*
htmlcov/
.tox/
.pytest_cache/
nosetests.xml
coverage.xml
# Django
*.log # Django specific logs if not in logs/
# db.sqlite3 # 通常开发数据库不提交，生产使用其他DB
# staticfiles/ # 收集的静态文件
# media/ # 用户上传的媒体文件
# Celery
celerybeat-schedule
# Jupyter Notebook
.ipynb_checkpoints

# Node.js
node_modules/
# package-lock.json # 通常建议提交
# yarn.lock # 通常建议提交
# logs/ # 已在通用部分
# .env # 包含敏感信息

# Ruby / Rails
.bundle/
vendor/bundle/
# Gemfile.lock # 通常建议提交
log/ # Rails log directory
tmp/
# config/database.yml # 通常不提交，使用示例文件
# config/master.key # 绝不提交
# config/credentials.yml.enc # 加密文件可提交，但 key 文件不提交
public/system
public/uploads
coverage/
spec/reports/
.rspec
rerun.txt
capybara-*.html
.powenv

# Java (Maven/Gradle)
# target/ # 已在通用部分 (Maven)
# build/  # 已在通用部分 (Gradle)
# *.jar   # 已在通用部分
# *.war   # 已在通用部分
# *.ear   # 已在通用部分
.gradle/
# local.properties # Android Studio / Gradle local settings

# C/C++
# build/ # 已在通用部分
# *.o    # 已在通用部分
# *.a    # 已在通用部分
# *.so   # 已在通用部分
# *.exe  # 已在通用部分
# *.dll  # 已在通用部分
cmake-build-*/
CMakeFiles/
CMakeCache.txt
Makefile # 如果是自动生成的

# PHP / Composer
vendor/
# composer.lock # 通常建议提交
# .env # 包含敏感信息

# Go
*.exe # Windows
*.exe~ # Windows
*.dll # Windows
*.so # Linux
*.dylib # Mac
# 通常 Go 项目会将可执行文件命名为项目名，这些如果不需要提交可以加上
# <projectname>
# vendor/ # Go modules 会管理依赖，通常不提交 vendor 除非特定情况

# Swift
.build/
DerivedData/
*.xcodeproj/project.xcworkspace/xcuserdata/
*.xcodeproj/xcuserdata/
*.xcworkspace/xcuserdata/
*.swiftpm/
Package.resolved # 通常建议提交

# -----------------------------------------------------------------------------
# 依赖管理 (Dependency Management)
# -----------------------------------------------------------------------------
# (大部分已在语言特定部分中列出，例如 node_modules, vendor, .venv)

# -----------------------------------------------------------------------------
# 测试报告和覆盖率 (Test Reports and Coverage)
# -----------------------------------------------------------------------------
coverage/
*.cover
nyc_output/
.nyc_output/
lcov.info
*.prof
*.profraw
# (许多已在语言特定部分中列出)

# -----------------------------------------------------------------------------
# 数据库文件 (Database files)
# -----------------------------------------------------------------------------
*.sqlite
*.sqlite3
# *.db # 通用数据库文件，但要小心，有些项目可能用 .db 做其他用途
*.sql # 如果不是迁移脚本或 schema 定义，则不提交
*.rdb # Redis dump
mongod.lock # MongoDB lock file

# -----------------------------------------------------------------------------
# 配置文件和敏感信息 (Configuration files and Sensitive information)
# -----------------------------------------------------------------------------
# 强烈建议：永远不要将包含密码、API密钥等敏感信息的文件提交到版本库！
# 使用环境变量或创建配置模板（例如 config.example.json）
*.env
.env.*
!/.env.example # 通常会提交一个 .env.example 文件作为模板
config/master.key # Rails
config/secrets.yml # Rails (unencrypted)
credentials.*
secrets.*
*.pem
*.key
aws/credentials
# 私有 SSH keys
id_rsa
id_dsa
# GPG keys
secring.*
# Put all sensitive files in a folder called 'secrets' and add 'secrets/' to .gitignore

# -----------------------------------------------------------------------------
# 用户特定或本地配置 (User-specific or local configurations)
# -----------------------------------------------------------------------------
*.local
local.*.props
.DS_Store? # macOS

# -----------------------------------------------------------------------------
# 大型媒体文件 (Large Media Files - consider Git LFS for these if needed)
# -----------------------------------------------------------------------------
# *.mp4 # 你之前遇到的问题，如果不想用 LFS 且不提交，则保留
# *.mov
# *.avi
# *.mp3
# *.wav
# *.flac
# *.psd
# *.ai
# *.sketch
# *.fig
# *.xd
# *.zip # 如果是大型数据集或构建产物
# *.gz
# *.tar.gz

# -----------------------------------------------------------------------------
# 其他 (Miscellaneous)
# -----------------------------------------------------------------------------
# Thumbs.db # Windows thumbnail cache
# ehthumbs.db # Windows thumbnail cache
# Desktop.ini # Windows folder metadata
# .terraform/ # Terraform state files (often managed remotely)
# *.tfstate
# *.tfstate.backup

# -----------------------------------------------------------------------------
# 特定项目文件 (Project Specific - ADD YOURS HERE!)
# -----------------------------------------------------------------------------
# my_project_specific_temp_file.xyz
# data_exports/
# generated_reports/

logs/
upload_cookies/
crawler/crawler_cookies/*
test/

crawler/test/*
crawler/Download/*


*.db
*.mp3
*.mp4
*.webp
