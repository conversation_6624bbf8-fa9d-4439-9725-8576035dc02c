import datetime
import json
from time import sleep

from playwright.sync_api import sync_playwright

from xhs import DataFetchError, XhsClient, help


def sign(uri, data=None, a1="", web_session=""):
    for _ in range(10):
        try:
            with sync_playwright() as playwright:
                stealth_js_path = "xhs_upload_js/stealth.min.js"
                chromium = playwright.chromium

                # 如果一直失败可尝试设置成 False 让其打开浏览器，适当添加 sleep 可查看浏览器状态
                browser = chromium.launch(headless=True)
                browser_context = browser.new_context()
                browser_context.add_init_script(path=stealth_js_path)
                context_page = browser_context.new_page()
                context_page.goto("https://www.xiaohongshu.com")
                browser_context.add_cookies([
                    {'name': 'a1', 'value': a1, 'domain': ".xiaohongshu.com", 'path': "/"}]
                )
                context_page.reload()
                # 这个地方设置完浏览器 cookie 之后，如果这儿不 sleep 一下签名获取就失败了，如果经常失败请设置长一点试试
                sleep(3)
                encrypt_params = context_page.evaluate("([url, data]) => window._webmsxyw(url, data)", [uri, data])
                return {
                    "x-s": encrypt_params["X-s"],
                    "x-t": str(encrypt_params["X-t"])
                }
        except Exception:
            # 这儿有时会出现 window._webmsxyw is not a function 或未知跳转错误，因此加一个失败重试趴
            pass
    raise Exception("重试了这么多次还是无法签名成功，寄寄寄")
    
def test_create_video_note(xhs_client: XhsClient):
    note = xhs_client.create_video_note(title="123123", video_path="C:\\Users\\<USER>\\Desktop\\tempfile\\CRM弹屏.png", desc="", is_private=True)
    print(note)


if __name__ == '__main__':
    cookie = "abRequestId=8fe64471-315a-53e8-a42a-7ba8f532d068; webBuild=4.62.3; xsecappid=xhs-pc-web; a1=196d951654bzzlh6x9wngiiqh860o7q78zf55jzw250000105115; webId=859821d17473969471b8698e71c3852b; gid=yjKfj2y0fYYyyjKfj2yK2uVu4Duu1xKCjEIkiEJ33AxYK2288lWAW2888y82yy280WJKJfSf; web_session=04006955fffaa67b2d826308123a4b3ad629a0; loadts=1747406735094; acw_tc=0ad52f3317474067347654848efd55f9448ac66aed40a9a4dc98ea4d833eaa; websectiga=9730ffafd96f2d09dc024760e253af6ab1feb0002827740b95a255ddf6847fc8; sec_poison_id=f64ec210-7ef5-4ddd-88f6-108eba3f8b55; unread={%22ub%22:%226800d729000000000f033393%22%2C%22ue%22:%226820906b0000000022007e9d%22%2C%22uc%22:29}"

    xhs_client = XhsClient(cookie, sign=sign)
    
    print(xhs_client.get_self_info())
    print("-----------------")
    print(datetime.datetime.now())

    for _ in range(10):
        # 即便上面做了重试，还是有可能会遇到签名失败的情况，重试即可
        try:
            note = xhs_client.create_video_note(title="123123", video_path="C:\\Users\\<USER>\\Desktop\\tempfile\\CRM弹屏录屏.mp4", desc="",
                                                cover_path="C:\\Users\\<USER>\\Desktop\\tempfile\\CRM弹屏.png",
                                                is_private=True)
            print(note)
            break
        except DataFetchError as e:
            print(e)
            print("失败重试一下下")

